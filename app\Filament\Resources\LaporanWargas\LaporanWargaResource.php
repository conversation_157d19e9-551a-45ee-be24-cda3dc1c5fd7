<?php

namespace App\Filament\Resources\LaporanWargas;

use App\Filament\Resources\LaporanWargas\Pages\CreateLaporanWarga;
use App\Filament\Resources\LaporanWargas\Pages\EditLaporanWarga;
use App\Filament\Resources\LaporanWargas\Pages\ListLaporanWargas;
use App\Filament\Resources\LaporanWargas\Schemas\LaporanWargaForm;
use App\Filament\Resources\LaporanWargas\Tables\LaporanWargasTable;
use App\Models\LaporanWarga;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class LaporanWargaResource extends Resource
{
    protected static ?string $model = LaporanWarga::class;

    protected static ?string $navigationLabel = 'Laporan Warga';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::Newspaper;

    public static function form(Schema $schema): Schema
    {
        return LaporanWargaForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return LaporanWargasTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListLaporanWargas::route('/'),
            'create' => CreateLaporanWarga::route('/create'),
            'edit' => EditLaporanWarga::route('/{record}/edit'),
        ];
    }
}
