import { queryParams, type QueryParams } from './../../wayfinder'
/**
* @see \App\Http\Controllers\Admin\Cash\CashController::index
 * @see app/Http/Controllers/Admin/Cash/CashController.php:14
 * @route '/admin/cash'
 */
export const index = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: index.url(options),
    method: 'get',
})

index.definition = {
    methods: ['get','head'],
    url: '/admin/cash',
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::index
 * @see app/Http/Controllers/Admin/Cash/CashController.php:14
 * @route '/admin/cash'
 */
index.url = (options?: { query?: QueryParams, mergeQuery?: QueryParams }) => {
    return index.definition.url + queryParams(options)
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::index
 * @see app/Http/Controllers/Admin/Cash/CashController.php:14
 * @route '/admin/cash'
 */
index.get = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: index.url(options),
    method: 'get',
})
/**
* @see \App\Http\Controllers\Admin\Cash\CashController::index
 * @see app/Http/Controllers/Admin/Cash/CashController.php:14
 * @route '/admin/cash'
 */
index.head = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'head',
} => ({
    url: index.url(options),
    method: 'head',
})

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::create
 * @see app/Http/Controllers/Admin/Cash/CashController.php:22
 * @route '/admin/cash/create'
 */
export const create = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: create.url(options),
    method: 'get',
})

create.definition = {
    methods: ['get','head'],
    url: '/admin/cash/create',
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::create
 * @see app/Http/Controllers/Admin/Cash/CashController.php:22
 * @route '/admin/cash/create'
 */
create.url = (options?: { query?: QueryParams, mergeQuery?: QueryParams }) => {
    return create.definition.url + queryParams(options)
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::create
 * @see app/Http/Controllers/Admin/Cash/CashController.php:22
 * @route '/admin/cash/create'
 */
create.get = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: create.url(options),
    method: 'get',
})
/**
* @see \App\Http\Controllers\Admin\Cash\CashController::create
 * @see app/Http/Controllers/Admin/Cash/CashController.php:22
 * @route '/admin/cash/create'
 */
create.head = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'head',
} => ({
    url: create.url(options),
    method: 'head',
})

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::store
 * @see app/Http/Controllers/Admin/Cash/CashController.php:30
 * @route '/admin/cash'
 */
export const store = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'post',
} => ({
    url: store.url(options),
    method: 'post',
})

store.definition = {
    methods: ['post'],
    url: '/admin/cash',
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::store
 * @see app/Http/Controllers/Admin/Cash/CashController.php:30
 * @route '/admin/cash'
 */
store.url = (options?: { query?: QueryParams, mergeQuery?: QueryParams }) => {
    return store.definition.url + queryParams(options)
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::store
 * @see app/Http/Controllers/Admin/Cash/CashController.php:30
 * @route '/admin/cash'
 */
store.post = (options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'post',
} => ({
    url: store.url(options),
    method: 'post',
})

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::show
 * @see app/Http/Controllers/Admin/Cash/CashController.php:38
 * @route '/admin/cash/{cash}'
 */
export const show = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: show.url(args, options),
    method: 'get',
})

show.definition = {
    methods: ['get','head'],
    url: '/admin/cash/{cash}',
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::show
 * @see app/Http/Controllers/Admin/Cash/CashController.php:38
 * @route '/admin/cash/{cash}'
 */
show.url = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }) => {
    if (typeof args === 'string' || typeof args === 'number') {
        args = { cash: args }
    }

    
    if (Array.isArray(args)) {
        args = {
                    cash: args[0],
                }
    }

    const parsedArgs = {
                        cash: args.cash,
                }

    return show.definition.url
            .replace('{cash}', parsedArgs.cash.toString())
            .replace(/\/+$/, '') + queryParams(options)
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::show
 * @see app/Http/Controllers/Admin/Cash/CashController.php:38
 * @route '/admin/cash/{cash}'
 */
show.get = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: show.url(args, options),
    method: 'get',
})
/**
* @see \App\Http\Controllers\Admin\Cash\CashController::show
 * @see app/Http/Controllers/Admin/Cash/CashController.php:38
 * @route '/admin/cash/{cash}'
 */
show.head = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'head',
} => ({
    url: show.url(args, options),
    method: 'head',
})

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::edit
 * @see app/Http/Controllers/Admin/Cash/CashController.php:46
 * @route '/admin/cash/{cash}/edit'
 */
export const edit = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: edit.url(args, options),
    method: 'get',
})

edit.definition = {
    methods: ['get','head'],
    url: '/admin/cash/{cash}/edit',
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::edit
 * @see app/Http/Controllers/Admin/Cash/CashController.php:46
 * @route '/admin/cash/{cash}/edit'
 */
edit.url = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }) => {
    if (typeof args === 'string' || typeof args === 'number') {
        args = { cash: args }
    }

    
    if (Array.isArray(args)) {
        args = {
                    cash: args[0],
                }
    }

    const parsedArgs = {
                        cash: args.cash,
                }

    return edit.definition.url
            .replace('{cash}', parsedArgs.cash.toString())
            .replace(/\/+$/, '') + queryParams(options)
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::edit
 * @see app/Http/Controllers/Admin/Cash/CashController.php:46
 * @route '/admin/cash/{cash}/edit'
 */
edit.get = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'get',
} => ({
    url: edit.url(args, options),
    method: 'get',
})
/**
* @see \App\Http\Controllers\Admin\Cash\CashController::edit
 * @see app/Http/Controllers/Admin/Cash/CashController.php:46
 * @route '/admin/cash/{cash}/edit'
 */
edit.head = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'head',
} => ({
    url: edit.url(args, options),
    method: 'head',
})

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::update
 * @see app/Http/Controllers/Admin/Cash/CashController.php:54
 * @route '/admin/cash/{cash}'
 */
export const update = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'put',
} => ({
    url: update.url(args, options),
    method: 'put',
})

update.definition = {
    methods: ['put','patch'],
    url: '/admin/cash/{cash}',
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::update
 * @see app/Http/Controllers/Admin/Cash/CashController.php:54
 * @route '/admin/cash/{cash}'
 */
update.url = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }) => {
    if (typeof args === 'string' || typeof args === 'number') {
        args = { cash: args }
    }

    
    if (Array.isArray(args)) {
        args = {
                    cash: args[0],
                }
    }

    const parsedArgs = {
                        cash: args.cash,
                }

    return update.definition.url
            .replace('{cash}', parsedArgs.cash.toString())
            .replace(/\/+$/, '') + queryParams(options)
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::update
 * @see app/Http/Controllers/Admin/Cash/CashController.php:54
 * @route '/admin/cash/{cash}'
 */
update.put = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'put',
} => ({
    url: update.url(args, options),
    method: 'put',
})
/**
* @see \App\Http\Controllers\Admin\Cash\CashController::update
 * @see app/Http/Controllers/Admin/Cash/CashController.php:54
 * @route '/admin/cash/{cash}'
 */
update.patch = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'patch',
} => ({
    url: update.url(args, options),
    method: 'patch',
})

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::destroy
 * @see app/Http/Controllers/Admin/Cash/CashController.php:62
 * @route '/admin/cash/{cash}'
 */
export const destroy = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'delete',
} => ({
    url: destroy.url(args, options),
    method: 'delete',
})

destroy.definition = {
    methods: ['delete'],
    url: '/admin/cash/{cash}',
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::destroy
 * @see app/Http/Controllers/Admin/Cash/CashController.php:62
 * @route '/admin/cash/{cash}'
 */
destroy.url = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }) => {
    if (typeof args === 'string' || typeof args === 'number') {
        args = { cash: args }
    }

    
    if (Array.isArray(args)) {
        args = {
                    cash: args[0],
                }
    }

    const parsedArgs = {
                        cash: args.cash,
                }

    return destroy.definition.url
            .replace('{cash}', parsedArgs.cash.toString())
            .replace(/\/+$/, '') + queryParams(options)
}

/**
* @see \App\Http\Controllers\Admin\Cash\CashController::destroy
 * @see app/Http/Controllers/Admin/Cash/CashController.php:62
 * @route '/admin/cash/{cash}'
 */
destroy.delete = (args: { cash: string | number } | [cash: string | number ] | string | number, options?: { query?: QueryParams, mergeQuery?: QueryParams }): {
    url: string,
    method: 'delete',
} => ({
    url: destroy.url(args, options),
    method: 'delete',
})
const cash = {
    index,
create,
store,
show,
edit,
update,
destroy,
}

export default cash