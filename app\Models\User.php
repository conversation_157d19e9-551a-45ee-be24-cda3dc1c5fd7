<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }


    // Relasi ke Warga (jika user adalah warga)
    public function warga()
    {
        return $this->hasOne(Warga::class);
    }

    // Relasi ke Transaksi yang dibuat user
    public function transaksi()
    {
        return $this->hasMany(Transaksi::class);
    }

    // Relasi ke Notifikasi
    public function notifikasi()
    {
        return $this->hasMany(Notifikasi::class);
    }

    // Relasi ke Laporan Keuangan yang dibuat user
    public function laporanKeuangan()
    {
        return $this->hasMany(LaporanKeuangan::class);
    }

    // Cek role user
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isBendahara()
    {
        return $this->role === 'bendahara';
    }

    public function isKetua()
    {
        return $this->role === 'ketua';
    }

    public function isWarga()
    {
        return $this->role === 'warga';
    }
    public function isUser()
    {
        return $this->role === 'user';
    }
}
