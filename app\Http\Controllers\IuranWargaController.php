<?php

namespace App\Http\Controllers;

use App\Models\IuranWarga;
use Illuminate\Http\Request;

class IuranWargaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(IuranWarga $iuranWarga)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(IuranWarga $iuranWarga)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, IuranWarga $iuranWarga)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(IuranWarga $iuranWarga)
    {
        //
    }
}
