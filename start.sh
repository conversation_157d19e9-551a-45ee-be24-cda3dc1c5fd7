#!/bin/bash

# Function to show usage
show_usage() {
    echo "Usage: $0 [dev|prod]"
    echo ""
    echo "Options:"
    echo "  dev   - Start in development mode (default)"
    echo "  prod  - Start in production mode"
    echo ""
    echo "Examples:"
    echo "  $0        # Start in development mode"
    echo "  $0 dev    # Start in development mode"
    echo "  $0 prod   # Start in production mode"
    exit 1
}

# Get mode from argument, default to dev
MODE=${1:-dev}

# Validate mode
if [[ "$MODE" != "dev" && "$MODE" != "prod" ]]; then
    echo "Error: Invalid mode '$MODE'"
    show_usage
fi

echo "Starting Laravel Octane with FrankenPHP in $MODE mode..."

# Stop existing containers
docker-compose down

if [ "$MODE" = "prod" ]; then
    echo "Building and starting in PRODUCTION mode..."
    echo "Node will build assets for production..."

    # Start with production override
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d

    echo "Containers started. Waiting for services to be ready..."

    # Wait for Node.js to build assets and cleanup
    echo "Waiting for Node.js to build assets and cleanup..."

    # Monitor node container until it exits (build completed)
    while [ "$(docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps -q node 2>/dev/null)" ]; do
        echo "Building assets..."
        sleep 3
    done

    echo "Asset build completed and Node.js dependencies cleaned up!"

    # Show final build logs
    echo "Final build logs:"
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs node | tail -5

    echo ""
    echo "Laravel Octane is now running in PRODUCTION mode!"
    echo "HTTP: http://localhost:8000"
    echo "HTTPS: https://localhost:8443"
    echo ""
    echo "Production commands:"
    echo "To view logs: docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f web"
    echo "To stop: docker-compose -f docker-compose.yml -f docker-compose.prod.yml down"
    echo ""
    echo "Note: Node.js container has exited after building assets and cleaning up dependencies."

else
    echo "Building and starting in DEVELOPMENT mode..."
    echo "Node will start dev server with hot reload..."

    # Start in development mode
    docker-compose up --build -d

    echo "Containers started. Waiting for services to be ready..."

    # Wait a bit for services to start
    sleep 5

    echo ""
    echo "Laravel Octane is now running in DEVELOPMENT mode!"
    echo "HTTP: http://localhost:8000"
    echo "HTTPS: https://localhost:8443"
    echo "Vite Dev Server: http://localhost:5173"
    echo ""
    echo "Development commands:"
    echo "To view logs: docker-compose logs -f web"
    echo "To view node logs: docker-compose logs -f node"
    echo "To stop: docker-compose down"
    echo ""
    echo "Hot reload is enabled for frontend assets!"
fi

echo ""
echo "Useful commands:"
echo "- Check all containers: docker-compose ps"
echo "- Restart specific service: docker-compose restart [service_name]"
echo "- View all logs: docker-compose logs -f"
