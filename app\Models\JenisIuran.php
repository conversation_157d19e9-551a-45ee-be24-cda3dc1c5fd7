<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JenisIuran extends Model
{
    use HasFactory;

    protected $guarded = [];

    // Relasi ke Iuran Warga
    public function iuranWarga()
    {
        return $this->hasMany(IuranWarga::class);
    }

    // Accessor untuk format jumlah uang
    public function getJumlahFormattedAttribute()
    {
        return 'Rp ' . number_format($this->jumlah, 0, ',', '.');
    }
}
