<script setup lang="ts">
import { useMagicKeys } from '@vueuse/core';
import { Search } from 'lucide-vue-next';

import { ref, watch } from 'vue';
import { CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator } from '@/components/ui/command';

const open = ref(false);

const { Meta_J, Ctrl_J } = useMagicKeys({
    passive: false,
    onEventFired(e) {
        if (e.key === 'j' && (e.metaKey || e.ctrlKey)) e.preventDefault();
    },
});

watch([Meta_J, Ctrl_J], (v) => {
    if (v[0] || v[1]) handleOpenChange();
});

function handleOpenChange() {
    open.value = !open.value;
}
</script>

<template>
    <div class="relative z-2">
        <div class="relative px-3 xs:px-6 pb-2">
            <div class="relative group cursor-pointer" @click.prevent="handleOpenChange()">
                <input
                    type="text"
                    class="h-12 w-full rounded-xl bg-white/50 dark:bg-black/30 backdrop-blur-sm group-hover:bg-white/70 pr-20 pl-12 outline-0 focus:border-0 focus:ring-0 placeholder:text-neutral-500 placeholder:text-sm"
                    placeholder="Cari..."
                    readonly
                />
                <Search class="absolute top-1/2 left-3 size-5 -translate-y-1/2 text-gray-500" />
                <kbd
                    class="text-muted-foreground absolute top-1/2 right-3 inline-flex -translate-y-1/2 items-center gap-1 rounded border px-1.5 font-mono text-sm font-medium"
                >
                    <span class="text-sm">⌘</span>J
                </kbd>
            </div>
        </div>

        <CommandDialog v-model:open="open" class="bg-primary">
            <CommandInput placeholder="Type a command or search..." />
            <CommandList>
                <CommandEmpty>No results found.</CommandEmpty>
                <CommandGroup heading="Suggestions">
                    <CommandItem value="calendar"> Calendar </CommandItem>
                    <CommandItem value="search-emoji"> Search Emoji </CommandItem>
                    <CommandItem value="calculator"> Calculator </CommandItem>
                </CommandGroup>
                <CommandSeparator />
                <CommandGroup heading="Settings">
                    <CommandItem value="profile"> Profile </CommandItem>
                    <CommandItem value="billing"> Billing </CommandItem>
                    <CommandItem value="settings"> Settings </CommandItem>
                </CommandGroup>
            </CommandList>
        </CommandDialog>
    </div>
</template>
