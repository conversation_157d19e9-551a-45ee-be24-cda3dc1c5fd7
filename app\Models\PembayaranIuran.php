<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PembayaranIuran extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'tanggal_bayar' => 'date',
    ];

    // Relasi ke Iuran Warga
    public function iuranWarga()
    {
        return $this->belongsTo(IuranWarga::class);
    }

    // Relasi ke Transaksi
    public function transaksi()
    {
        return $this->belongsTo(Transaksi::class);
    }

    // Accessor untuk format jumlah uang
    public function getJumlahFormattedAttribute()
    {
        return 'Rp ' . number_format($this->jumlah, 0, ',', '.');
    }
}
