<?php

namespace Database\Factories;

use App\Models\Warga;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class WargaFactory extends Factory
{
    protected $model = Warga::class;

    public function definition(): array
    {
        return [
            'nik' => strval(mt_rand(1000000000000000, 9999999999999999)), // 16 digit
            'nama_lengkap' => $this->faker->name(),
            'tempat_lahir' => $this->faker->city(),
            'tanggal_lahir' => $this->faker->date('Y-m-d', '-18 years'),
            'jenis_kelamin' => $this->faker->randomElement(['L', 'P']),
            'status_perkawinan' => $this->faker->randomElement(['belum_kawin', 'kawin', 'cerai_hidup', 'cerai_mati']),
            'agama' => $this->faker->randomElement(['Islam', 'Kristen', 'Katolik', 'Hindu', 'Buddha', '<PERSON><PERSON><PERSON>']),
            'pendidikan_terakhir' => $this->faker->randomElement(['SD', 'SMP', 'SMA', 'D3', 'S1', 'S2']),
            'pekerjaan' => $this->faker->jobTitle(),
            'alamat' => $this->faker->address(),
            'rt' => str_pad(rand(1, 10), 2, '0', STR_PAD_LEFT),
            'rw' => str_pad(rand(1, 10), 2, '0', STR_PAD_LEFT),
            'kelurahan' => $this->faker->citySuffix(),
            'kecamatan' => $this->faker->streetName(),
            'kota' => $this->faker->city(),
            'provinsi' => $this->faker->state(),
            'kode_pos' => $this->faker->postcode(),
            'no_telepon' => '08' . $this->faker->numerify('##########'),
            'status_warga' => 'aktif',
            'status_hubungan' => 'kepala_keluarga'
        ];
    }
}
