<?php

namespace App\Filament\Resources\Jurnals\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class JurnalForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('transaksi_id')
                    ->required()
                    ->numeric(),
                TextInput::make('coa_id')
                    ->required()
                    ->numeric(),
                TextInput::make('debit')
                    ->required()
                    ->numeric()
                    ->default(0.0),
                TextInput::make('kredit')
                    ->required()
                    ->numeric()
                    ->default(0.0),
                Textarea::make('keterangan')
                    ->columnSpanFull(),
            ]);
    }
}
