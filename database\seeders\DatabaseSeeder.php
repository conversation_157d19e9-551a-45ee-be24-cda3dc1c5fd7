<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        // Nonaktifkan foreign key check sementara
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        $this->call([
            UserSeeder::class,
            WargaSeeder::class,
            KeluargaSeeder::class,
            CoaSeeder::class,
            SaldoAwalSeeder::class,
            JenisIuranSeeder::class,
            IuranWargaSeeder::class,
            KategoriLaporanSeeder::class,
            LaporanWargaSeeder::class,
            NotifikasiSeeder::class,
        ]);

        // Aktifkan kembali foreign key check
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
}
