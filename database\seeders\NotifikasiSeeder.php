<?php

namespace Database\Seeders;

use App\Models\Notifikasi;
use App\Models\User;
use Illuminate\Database\Seeder;

class NotifikasiSeeder extends Seeder
{
    public function run()
    {
        Notifikasi::truncate();

        $users = User::all();

        foreach ($users as $user) {
            // Buat 1-3 notifikasi untuk setiap user
            $jumlah = rand(1, 3);

            for ($i = 0; $i < $jumlah; $i++) {
                Notifikasi::create([
                    'user_id' => $user->id,
                    'judul' => 'Notifikasi Contoh ' . ($i + 1),
                    'pesan' => 'Ini adalah contoh notifikasi untuk user ' . $user->name,
                    'tipe' => ['info', 'warning', 'success'][rand(0, 2)],
                    'dibaca' => rand(0, 1),
                ]);
            }
        }
    }
}
