<?php

namespace App\Filament\Resources\LaporanKeuangans\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class LaporanKeuanganForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('nama_laporan')
                    ->required(),
                Select::make('jenis_laporan')
                    ->options([
            'neraca' => 'Neraca',
            'laba_rugi' => 'Laba rugi',
            'arus_kas' => 'Arus kas',
            'perubahan_modal' => 'Perubahan modal',
        ])
                    ->required(),
                DatePicker::make('periode_awal')
                    ->required(),
                DatePicker::make('periode_akhir')
                    ->required(),
                TextInput::make('file_path'),
                Textarea::make('keterangan')
                    ->columnSpanFull(),
                TextInput::make('user_id')
                    ->required()
                    ->numeric(),
            ]);
    }
}
