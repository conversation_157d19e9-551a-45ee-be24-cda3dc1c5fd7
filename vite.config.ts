import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';
import path from 'path';
import tailwindcss from "@tailwindcss/vite";
import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import { wayfinder } from "@laravel/vite-plugin-wayfinder";

export default defineConfig({
    server: {
        host: '0.0.0.0', // penting agar bisa diakses dari host
        port: 5173, // default Vite port
        strictPort: true, // error jika port 5173 tidak tersedia
        hmr: {
            host: 'localhost', // bisa juga pakai '127.0.0.1' atau IP Docker
            protocol: 'ws',
        },
        watch: {
            usePolling: true,
        },

    },
    plugins: [
        laravel({
            input: ['resources/js/app.ts'],
            ssr: 'resources/js/ssr.ts',
            refresh: true,
        }),
        tailwindcss(),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
        wayfinder(),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js'),
            'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
        },
    },
});
