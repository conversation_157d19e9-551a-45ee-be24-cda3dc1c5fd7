<script setup lang="ts">
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAppearance } from '@/composables/useAppearance';
import { useMagicKeys } from '@vueuse/core';
;
import { computed, watch } from 'vue';

const { appearance, updateAppearance } = useAppearance();

const tabs = [
    { value: 'light', Icon: 'solar:sun-2-bold-duotone', label: 'Light', key: 'shift+cmd+l' },
    { value: 'dark', Icon: 'solar:moon-bold-duotone', label: 'Dark', key: 'shift+cmd+d' },
    { value: 'system', Icon: 'solar:monitor-bold-duotone', label: 'System', key: 'shift+cmd+s' },
] as const;

// Setup magic keys
const keys = useMagicKeys();

// Mensetup shortcut untuk setiap mode
tabs.forEach(tab => {
    const keyRef = keys[tab.key.replace(/\+/g, '_')];
    watch(keyRef, (pressed) => {
        if (pressed) {
            updateAppearance(tab.value);
        }
    });
});

// Fungsi untuk toggle mode secara otomatis (cycling)
const toggleMode = () => {
    const currentIndex = tabs.findIndex(tab => tab.value === appearance.value);
    const nextIndex = (currentIndex + 1) % tabs.length;
    updateAppearance(tabs[nextIndex].value);
};

// Shortcut untuk toggle otomatis (shift+cmd+t)
const { shift_cmd_t } = keys;
watch(shift_cmd_t, (pressed) => {
    if (pressed) {
        toggleMode();
    }
});

// Fungsi untuk mendapatkan shortcut text yang sesuai dengan OS
const getShortcutText = (shortcut: string) => {
        return shortcut.replace('shift+cmd+', '⇧⌘').toUpperCase();
    // const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);
    // if (isMac) {
    //     return shortcut.replace('shift+cmd+', '⇧⌘').toUpperCase();
    // } else {
    //     return shortcut.replace('shift+cmd+', 'Ctrl+Shift+').toUpperCase();
    // }
};

const activeIcon = computed(() => {
    return tabs.find((tab) => tab.value === appearance.value)?.Icon ?? 'solar:monitor-bold-duotone';
});
</script>

<template>
    <div class="relative z-2 !max-w-xl">
        <DropdownMenu>
            <DropdownMenuTrigger as-child>
                <div>
                    <Icon :icon="activeIcon" class="size-7 cursor-pointer text-white" />
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent class="w-40">
                <DropdownMenuGroup>
                    <DropdownMenuItem
                        v-for="mode in tabs"
                        :key="mode.value"
                        @click="updateAppearance(mode.value)"
                        :class="['flex items-center gap-2', appearance === mode.value ? 'text-primary font-semibold' : '']"
                    >
                        <Icon :icon="mode.Icon" class="size-4"/>
                        <span>{{ mode.label }}</span>
                        <DropdownMenuShortcut>{{ getShortcutText(mode.key) }}</DropdownMenuShortcut>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    </div>
</template>
