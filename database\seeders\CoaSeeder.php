<?php

namespace Database\Seeders;

use App\Models\Coa;
use App\Models\Jurnal;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CoaSeeder extends Seeder
{
    public function run()
    {
        // Hapus semua jurnal yang terkait terlebih dahulu
        Jurnal::truncate();

        // Baru kemudian truncate tabel coas
        Coa::truncate();

        // Akun Header
        $aset = Coa::create([
            'kode' => '1',
            'nama' => 'Aset',
            'kategori' => 'aset',
            'tipe' => 'header',
        ]);

        $kewajiban = Coa::create([
            'kode' => '2',
            'nama' => 'Kewajiban',
            'kategori' => 'kewajiban',
            'tipe' => 'header',
        ]);

        $ekuitas = Coa::create([
            'kode' => '3',
            'nama' => 'Ekuitas',
            'kategori' => 'ekuitas',
            'tipe' => 'header',
        ]);

        $pendapatan = Coa::create([
            'kode' => '4',
            'nama' => 'Pendapatan',
            'kategori' => 'pendapatan',
            'tipe' => 'header',
        ]);

        $beban = Coa::create([
            'kode' => '5',
            'nama' => 'Beban',
            'kategori' => 'beban',
            'tipe' => 'header',
        ]);

        // Akun Detail - Aset
        Coa::create([
            'kode' => '1.1',
            'nama' => 'Kas',
            'kategori' => 'aset',
            'tipe' => 'detail',
            'parent_id' => $aset->id,
        ]);

        Coa::create([
            'kode' => '1.2',
            'nama' => 'Bank',
            'kategori' => 'aset',
            'tipe' => 'detail',
            'parent_id' => $aset->id,
        ]);

        Coa::create([
            'kode' => '1.3',
            'nama' => 'Piutang Iuran',
            'kategori' => 'aset',
            'tipe' => 'detail',
            'parent_id' => $aset->id,
        ]);

        // Akun Detail - Kewajiban
        Coa::create([
            'kode' => '2.1',
            'nama' => 'Hutang',
            'kategori' => 'kewajiban',
            'tipe' => 'detail',
            'parent_id' => $kewajiban->id,
        ]);

        // Akun Detail - Ekuitas
        Coa::create([
            'kode' => '3.1',
            'nama' => 'Modal Awal',
            'kategori' => 'ekuitas',
            'tipe' => 'detail',
            'parent_id' => $ekuitas->id,
        ]);

        Coa::create([
            'kode' => '3.2',
            'nama' => 'Saldo Laba',
            'kategori' => 'ekuitas',
            'tipe' => 'detail',
            'parent_id' => $ekuitas->id,
        ]);

        // Akun Detail - Pendapatan
        Coa::create([
            'kode' => '4.1',
            'nama' => 'Iuran Warga',
            'kategori' => 'pendapatan',
            'tipe' => 'detail',
            'parent_id' => $pendapatan->id,
        ]);

        Coa::create([
            'kode' => '4.2',
            'nama' => 'Donasi',
            'kategori' => 'pendapatan',
            'tipe' => 'detail',
            'parent_id' => $pendapatan->id,
        ]);

        // Akun Detail - Beban
        Coa::create([
            'kode' => '5.1',
            'nama' => 'Administrasi',
            'kategori' => 'beban',
            'tipe' => 'detail',
            'parent_id' => $beban->id,
        ]);

        Coa::create([
            'kode' => '5.2',
            'nama' => 'Kegiatan RT',
            'kategori' => 'beban',
            'tipe' => 'detail',
            'parent_id' => $beban->id,
        ]);

        Coa::create([
            'kode' => '5.3',
            'nama' => 'Pemeliharaan',
            'kategori' => 'beban',
            'tipe' => 'detail',
            'parent_id' => $beban->id,
        ]);

    }
}
