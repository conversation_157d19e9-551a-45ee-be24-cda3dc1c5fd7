<script setup lang="ts">
import type { HTMLAttributes } from 'vue';

defineOptions({
    inheritAttrs: false,
});

interface Props {
    className?: HTMLAttributes['class'];
}

defineProps<Props>();
</script>

<template>
    <svg id="Layer_2" :class="className" v-bind="$attrs" fill="currentColor" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 70.97 104.65">
        <g id="Layer_2-2" data-name="Layer 2">
            <polygon class="cls-1" points="50.62 30.47 31.51 45.41 70.97 74.93 70.97 45.82 50.62 30.47"/>
            <polygon class="cls-2" points="70.97 45.82 31.16 75.43 31.16 104.65 70.97 74.93 70.97 45.82"/>
            <polygon class="cls-1" points="0 31.09 39.93 59.87 19.62 74.93 0 59.87 0 31.09"/>
            <g>
                <polygon class="cls-2" points="19.01 15.8 19.31 45.6 .07 60.03 .07 30.6 19.01 15.8"/>
                <path class="cls-2" d="M24,24.46c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,27c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,16.84c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,19.38c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,37.16c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,34.62c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,32.08c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,29.54c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,14.3c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,21.92c1.43-.13,1.78,1.98.39,2.31-1.62.39-2.04-2.16-.39-2.31Z"/>
                <path class="cls-2" d="M24,39.7c.79-.07,1.47.69,1.24,1.47l-1.15.86c-.16.08-.57-.12-.71-.23-.83-.65-.42-2,.62-2.1Z"/>
                <path class="cls-2" d="M24.18,11.79c.15-.03.53.15.66.25,1.2.93-.21,2.8-1.45,1.86-.32-.25-.6-.82-.39-1.2l1.17-.9Z"/>
                <path class="cls-2" d="M26.54,16.97c1.43-.15,1.61,2.06.17,2.15-1.37.09-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,24.56c1.43-.15,1.61,2.06.17,2.15s-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,37.26c1.43-.15,1.61,2.06.17,2.15-1.37.09-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,19.51c1.43-.15,1.61,2.06.17,2.15s-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,34.72c1.43-.15,1.61,2.06.17,2.15-1.37.09-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,11.89c1.43-.15,1.61,2.06.17,2.15s-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,32.18c1.43-.15,1.61,2.06.17,2.15-1.37.09-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,29.64c1.43-.15,1.61,2.06.17,2.15-1.37.09-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,14.43c1.43-.15,1.61,2.06.17,2.15s-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,27.1c1.43-.15,1.61,2.06.17,2.15s-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M26.54,22.05c1.43-.15,1.61,2.06.17,2.15s-1.56-2-.17-2.15Z"/>
                <path class="cls-2" d="M29.18,32.31c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,19.62c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,17.08c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,27.23c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,24.69c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,12c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,14.54c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,34.83c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,9.46c1.26-.01,1.29,1.92.02,1.93-1.26.01-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,29.77c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M29.18,22.15c1.26-.01,1.29,1.92.02,1.93s-1.29-1.92-.02-1.93Z"/>
                <path class="cls-2" d="M25.6,10.66l1.55-1.17c.78.38.75,1.54-.03,1.91-.64.3-1.36-.06-1.52-.73Z"/>
                <path class="cls-2" d="M31.67,32.44c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,9.58c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,14.66c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,17.2c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,19.74c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,22.28c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,12.12c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,7.04c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,27.36c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,24.82c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,29.9c.94-.07,1.24,1.29.38,1.61-1.23.47-1.65-1.52-.38-1.61Z"/>
                <path class="cls-2" d="M31.67,34.98c.41-.03.83.28.87.7-.15.1-1.13.9-1.21.89-.09-.01-.28-.23-.33-.31-.31-.55.04-1.22.67-1.27Z"/>
                <path class="cls-2" d="M34.18,4.66c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,7.2c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,22.43c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,32.59c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,30.05c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,12.28c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,19.89c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,9.74c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,14.81c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,24.97c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,27.51c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M34.18,17.35c.93-.11,1.05,1.33.12,1.38-.86.05-1-1.27-.12-1.38Z"/>
                <path class="cls-2" d="M29.94,7.29c.6.79-.22,1.86-1.14,1.47-.09-.04-.42-.23-.34-.33l1.48-1.14Z"/>
                <path class="cls-2" d="M28.34,38.82c-.49-1.02.69-1.93,1.54-1.17l-1.54,1.17Z"/>
                <path class="cls-2" d="M21.67,14.19c-.06.03-.14.02-.21,0,0,0-.02,0-.03,0-.08,0-.15-.02-.23-.04-.03,0-.08,0-.04-.03l1.65-1.28c.04.74-.43,1.27-1.15,1.36Z"/>
                <path class="cls-2" d="M36.67,12.48c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,15.02c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,22.64c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,27.72c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,25.18c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,30.26c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,20.1c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,17.56c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,2.32c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,4.86c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,9.94c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M36.67,7.4c.81-.15.85,1.06.05.97-.52-.06-.53-.88-.05-.97Z"/>
                <path class="cls-2" d="M26.98,39.85l-1.41,1.05c0-.75.7-1.27,1.41-1.05Z"/>
                <path class="cls-2" d="M32.57,5.27c.02.62-.53,1.06-1.13.87l1.13-.87Z"/>
                <path class="cls-2" d="M39.28,20.33c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,27.95c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,12.71c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,10.17c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,15.25c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,7.63c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,5.09c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,2.55c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,17.79c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,25.41c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28,22.87c.38-.08.4.56.02.51-.27-.04-.29-.45-.02-.51Z"/>
                <path class="cls-2" d="M39.28.01c.3-.09.42.44.12.5-.2.04-.4-.19-.29-.36,0-.01.16-.13.17-.14Z"/>
                <path class="cls-2" d="M39.42,30.48c.01.06-.03.06-.06.08-.08.08-.19.13-.27.2-.01-.18.16-.31.33-.28Z"/>
                <path class="cls-2" d="M25.05,11.11c-.12.15-.27.31-.46.36l.06-.08.4-.28Z"/>
                <path class="cls-2" d="M23.62,42.36l-.38.28c.09-.13.23-.24.38-.28Z"/>
                <path class="cls-2" d="M21.67,14.19v.03c-.07,0-.16.02-.21-.03.06,0,.14,0,.21,0Z"/>
                <path class="cls-2" d="M21.67,24.35v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,26.89v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,39.59v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,29.43v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,21.81v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,31.97v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,34.51v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,42.13v.03c-.07,0-.15.02-.21-.01,0-.03.17,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,16.73v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,19.27v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,37.05v.03s-.21.01-.21-.01c.06-.03.14,0,.21-.01Z"/>
                <path class="cls-2" d="M21.67,19.27c1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03.8-.08,1.32-.8,1.12-1.59-.13-.51-.59-.89-1.12-.92-.07,0-.13-.02-.21-.03-.01-.02-.02-.02-.03,0-.08.01-.29.09-.38.12-.31.13-.57.4-.68.72-.07-.01-.08-.23-.14-.2l-2.14,1.68.08.08.26.17c-.23.07-.4.26-.55.44-.02,0-.19-.37-.29-.3L.04,30.57v29.46l19.97-14.96s.07-.04.05-.08c-.02-.06-.18-.13-.22-.19l-.23-.14c.37-.13.6-.47.76-.82.06.12.11.24.19.35.04.06.23.25.29.24l1.92-1.41c-.17-.5-.58-.82-1.1-.87-.1,0-.1-.01,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03,1.54-.15,1.54-2.37,0-2.51-.02,0-.02-.02,0-.03ZM21.08,42.25c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,39.71c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,37.17c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,34.63c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,32.09c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,29.55c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,27.01c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,24.47c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,21.93c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,19.39c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12ZM21.08,16.85c-.32.13-.59.4-.69.73-.02.02-.15-.27-.17-.31-.14-.23-.36-.42-.6-.53.37-.16.6-.47.76-.83.17.45.58.78,1.06.82l-.36.12Z"/>
            </g>
        </g>
    </svg>
</template>
