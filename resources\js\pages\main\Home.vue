<script setup lang="ts">
import AppLayout from '@/layouts/MainLayout.vue';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';
import Profile from '@/components/main/profile.vue';
import Info from '@/components/main/info.vue';
import Sheet from '@/components/main/sheet.vue';
import HighlightMenu from '@/components/main/highlight-menu.vue';
import Search from '@/components/main/search.vue';
import Navbar from '@/layouts/main/Navbar.vue';
import Shape from '@/components/Shape.vue';
import type { User } from '@/types';
import ToggleMode from '@/components/ToggleMode.vue';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard'
    }
];

const page = usePage<SharedData>();
const user = page.props.auth.user as User;

</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl">
            <Shape class="overflow-hidden md:rounded-t-4xl" />
            <Navbar :user="user" />
            <div class="flex justify-between items-center px-3 xs:px-6 py-8">
                <Profile :user="user" />
                <ToggleMode />
            </div>
            <Search />
            <HighlightMenu />
            <Info />
        </div>
    </AppLayout>
</template>
