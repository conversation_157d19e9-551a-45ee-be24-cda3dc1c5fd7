<?php

namespace App\Filament\Resources\Saldos\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class SaldoForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('coa_id')
                    ->required()
                    ->numeric(),
                DatePicker::make('periode')
                    ->required(),
                TextInput::make('saldo_awal')
                    ->required()
                    ->numeric()
                    ->default(0.0),
                TextInput::make('saldo_akhir')
                    ->required()
                    ->numeric()
                    ->default(0.0),
            ]);
    }
}
