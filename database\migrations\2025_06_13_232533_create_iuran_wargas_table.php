<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('iuran_wargas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('jenis_iuran_id')->constrained()->onDelete('cascade');
            $table->foreignId('keluarga_id')->constrained()->onDelete('cascade');
            $table->date('tanggal_jatuh_tempo');
            $table->enum('status', ['belum_lunas', 'lunas', 'tertunggak']);
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('iuran_wargas');
    }
};
