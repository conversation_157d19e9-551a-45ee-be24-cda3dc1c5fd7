<!-- PengaturanDrawer.vue -->
<script setup lang="ts">
import { Drawer, DrawerContent } from '@/components/ui/drawer'
import { watch, defineProps } from 'vue'
import { LogOut } from 'lucide-vue-next';
import { destroy } from '@/actions/App/Http/Controllers/Auth/AuthenticatedSessionController';
import { Link } from '@inertiajs/vue3';

const props = defineProps<{ open: boolean }>()
const model = defineModel<boolean>()

watch(() => props.open, (val) => {
    model.value = val
})
</script>

<template>
        <Drawer v-model:open="model">
            <DrawerContent>
                <div class="mb-6 mt-4 mx-4">

                        <Link class="flex justify-start items-center w-full" method="post" :href="destroy().url" as="button">
                            <LogOut class="mr-2 h-4 w-4" />
                            Log out
                        </Link>

                </div>
            </DrawerContent>
        </Drawer>
</template>
