<template>
    <div
        :class="cn('pointer-events-none absolute inset-0', props.class)"
    >
        <!-- Shape One -->
        <div
            :class="cn(
    'absolute h-[250px] w-full rounded-b-[3rem] sm:rounded-b-[4rem]',
    'bg-gradient-to-bl from-[#57e0aa] to-[#48c5fb]',
    'dark:from-[#1a3f35] dark:to-[#123c4e]', // jauh lebih gelap
    props.shapeOne
  )">
        </div>

        <!-- Shape Two -->
        <div
            :class="cn(
    'absolute h-[350px] w-[350px] -left-10 -top-44 rounded-full',
    'bg-gradient-to-b from-[#45c6ff] to-[#1677fa]',
    'dark:from-[#0e324a] dark:to-[#0a2a63]', // jauh lebih gelap
    props.shapeTwo
  )">
        </div>


    </div>
</template>

<script setup lang="ts">

import { cn } from '@/lib/utils'
interface Props{
    class?: string,
    shapeOne?: string,
    shapeTwo?: string,
}

const props = defineProps<Props>();

</script>
