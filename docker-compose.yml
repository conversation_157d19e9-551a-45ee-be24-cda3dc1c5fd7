services:
    web:
        container_name: kampung_project
        build:
            context: .
            dockerfile: docker/frankenphp/worker.dockerfile
            args:
                INSTALL_DEV: "true"
        ports:
            - "8000:8000"
        env_file:
            - .env.docker
        volumes:
            - .:/kampung_project:rw
            - storage_data:/kampung_project/storage  # Named volume untuk storage
            - cache_data:/kampung_project/bootstrap/cache  # Named volume untuk
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy

    postgres:
        image: postgres:16-alpine
        container_name: kampung_project_postgres
        restart: unless-stopped
        environment:
            POSTGRES_DB: kampung_project
            POSTGRES_USER: kampung_project
            POSTGRES_PASSWORD: shei1ePaidaa7eighaeh0sec0Au2yah7
        ports:
            - "5432:5432"
        volumes:
            - pgdata:/var/lib/postgresql/data
        healthcheck:
            test: ["CMD-SHELL", "pg_isready -U laravel -d laravel"]
            interval: 10s
            timeout: 5s
            retries: 5
            start_period: 30s


    redis:
        image: redis:7-alpine
        container_name: kampung_project_redis
        restart: unless-stopped
        ports:
            - "6379:6379"
        healthcheck:
            test: ["CMD", "redis-cli", "ping"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s

    node:
        image: node:22-alpine
        working_dir: /kampung_project
        container_name: kampung_project_node
        volumes:
            - .:/kampung_project
            - /kampung_project/node_modules
        command: sh -c "npm install && npm run dev"
        ports:
            - "5173:5173"
        depends_on:
            - web
        environment:
            - NODE_ENV=${NODE_ENV:-development}

volumes:
    pgdata:
    vendor:
    node_modules:
    storage_data:
    cache_data:
