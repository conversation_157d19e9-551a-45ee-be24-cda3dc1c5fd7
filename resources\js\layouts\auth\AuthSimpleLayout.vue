<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { Link } from '@inertiajs/vue3';
import Shape from '@/components/Shape.vue';
import ToggleMode from '@/components/ToggleMode.vue';
import { index } from '@/actions/App/Http/Controllers/Main/HomeController';

defineProps<{
    title?: string;
    description?: string;
}>();
</script>

<template>
    <div class="flex justify-center items-center relative mainBg max-w-xl mx-auto min-h-screen md:border-2 md:rounded-4xl px-6 xs:px-8">
        <Shape
            class="overflow-hidden md:rounded-t-4xl"
            shape-one="h-[400px] xs:h-[450px] sm:h-[550px]"
            shape-two="xs:h-[400px] xs:w-[400px] sm:h-[450px] sm:w-[450px] -left-10 -top-16 xs:-top-20"
        />
        <div class="absolute top-8 right-8">
            <ToggleMode/>
        </div>

        <div class="w-full max-w-sm mx-auto relative z-1">
            <div class="flex flex-col gap-8">
                <div class="flex flex-col items-center gap-4">
                    <Link :href="index.url()" class="flex flex-col items-center gap-2 font-medium">
                        <div class="mb-1 flex h-9 w-9 items-center justify-center rounded-md">
                            <AppLogoIcon class="size-9 fill-current text-[var(--foreground)] dark:text-white" />
                        </div>
                        <span class="sr-only">{{ title }}</span>
                    </Link>
                    <div class="space-y-2 text-center">
                        <h1 class="text-xl font-medium text-white">{{ title }}</h1>
                        <p class="text-center text-sm  text-white">{{ description }}</p>
                    </div>
                </div>
                <slot />
            </div>
        </div>
    </div>
</template>
