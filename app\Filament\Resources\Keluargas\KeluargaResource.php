<?php

namespace App\Filament\Resources\Keluargas;

use App\Filament\Resources\Keluargas\Pages\CreateKeluarga;
use App\Filament\Resources\Keluargas\Pages\EditKeluarga;
use App\Filament\Resources\Keluargas\Pages\ListKeluargas;
use App\Filament\Resources\Keluargas\Schemas\KeluargaForm;
use App\Filament\Resources\Keluargas\Tables\KeluargasTable;
use App\Models\Keluarga;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class KeluargaResource extends Resource
{
    protected static string|null|\UnitEnum $navigationGroup = 'Data Warga';

    protected static ?string $model = Keluarga::class;

    protected static ?string $navigationLabel = 'Keluarga';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::HomeModern;


    public static function form(Schema $schema): Schema
    {
        return KeluargaForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return KeluargasTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListKeluargas::route('/'),
            'create' => CreateKeluarga::route('/create'),
            'edit' => EditKeluarga::route('/{record}/edit'),
        ];
    }
}
