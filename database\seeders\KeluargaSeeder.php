<?php

namespace Database\Seeders;

use App\Models\Keluarga;
use App\Models\Warga;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class KeluargaSeeder extends Seeder
{
    public function run()
    {
        // Nonaktifkan foreign key check sementara

        Keluarga::truncate();

        // Reset keluarga_id dan status_hubungan di warga
        Warga::query()->update([
            'keluarga_id' => null,
            'status_hubungan' => 'kepala_keluarga'
        ]);

        // Ambil semua warga sebagai array
        $wargas = Warga::whereNull('keluarga_id')->get()->all();
        $no_kk = 1;

        while (count($wargas) > 0) {
            // Ambil warga pertama sebagai kepala keluarga
            $kepalaKeluarga = array_shift($wargas);

            // Buat KK baru
            $keluarga = Keluarga::create([
                'no_kk' => '32' . mt_rand(1000, 9999) . mt_rand(1000, 9999) . mt_rand(1000, 9999),
                'kepala_keluarga_id' => $kepalaKeluarga->id,
                'alamat' => 'Jl. Keluarga No.' . $no_kk++ . ' RT 01 RW 05',
                'rt' => '01',
                'rw' => '05',
            ]);

            // Set kepala keluarga
            $kepalaKeluarga->update([
                'keluarga_id' => $keluarga->id,
                'status_hubungan' => 'kepala_keluarga'
            ]);

            // Tambahkan istri (jika ada dan jenis kelamin kepala keluarga L)
            if ($kepalaKeluarga->jenis_kelamin === 'L') {
                $istriKey = $this->findWargaByGender($wargas, 'P');
                if ($istriKey !== null) {
                    $istri = $wargas[$istriKey];
                    unset($wargas[$istriKey]);
                    $wargas = array_values($wargas); // Re-index array

                    $istri->update([
                        'keluarga_id' => $keluarga->id,
                        'status_hubungan' => 'istri'
                    ]);
                }
            }

            // Tambahkan anak (1-3 orang)
            $jumlahAnak = min(rand(1, 3), count($wargas));
            for ($i = 0; $i < $jumlahAnak; $i++) {
                if (count($wargas) > 0) {
                    $anak = array_shift($wargas);
                    $anak->update([
                        'keluarga_id' => $keluarga->id,
                        'status_hubungan' => 'anak'
                    ]);
                }
            }

            // 20% kemungkinan ada anggota lain (orang tua/mertua)
            if (rand(1, 5) === 1 && count($wargas) > 0) {
                $anggotaLain = array_shift($wargas);
                $statusHubungan = ['orang_tua', 'mertua', 'famili_lain'][rand(0, 2)];

                $anggotaLain->update([
                    'keluarga_id' => $keluarga->id,
                    'status_hubungan' => $statusHubungan
                ]);
            }
        }

        // Aktifkan kembali foreign key check
    }

    protected function findWargaByGender(&$wargas, $gender)
    {
        foreach ($wargas as $key => $warga) {
            if ($warga->jenis_kelamin === $gender) {
                return $key;
            }
        }
        return null;
    }
}
