<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Warga extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'tanggal_lahir' => 'date',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function keluarga()
    {
        return $this->belongsTo(Keluarga::class);
    }

    public function isKepalaKeluarga()
    {
        return optional($this->keluarga)->kepala_keluarga_id === $this->id;
    }

    public function anggotaKeluarga()
    {
        if (!$this->keluarga_id) return collect();

        return Warga::where('keluarga_id', $this->keluarga_id)
            ->where('id', '!=', $this->id)
            ->get();
    }

    public function laporan(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(LaporanWarga::class);
    }

    public function iuran(): \Illuminate\Database\Eloquent\Relations\HasManyThrough
    {
        return $this->hasManyThrough(
            IuranWarga::class,
            AnggotaKeluarga::class,
            'warga_id',
            'keluarga_id',
            'id',
            'keluarga_id'
        );
    }

    public function getUsiaAttribute()
    {
        return $this->tanggal_lahir->age;
    }


    public function getStatusHubunganLabelAttribute()
    {
        $labels = [
            'kepala_keluarga' => 'Kepala Keluarga',
            'istri' => 'Istri',
            'anak' => 'Anak',
            'menantu' => 'Menantu',
            'cucu' => 'Cucu',
            'orang_tua' => 'Orang Tua',
            'mertua' => 'Mertua',
            'famili_lain' => 'Famili Lain',
            'pembantu' => 'Pembantu',
            'lainnya' => 'Lainnya'
        ];

        return $labels[$this->status_hubungan] ?? $this->status_hubungan;
    }
}
