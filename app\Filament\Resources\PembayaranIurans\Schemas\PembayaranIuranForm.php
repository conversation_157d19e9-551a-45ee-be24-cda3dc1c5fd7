<?php

namespace App\Filament\Resources\PembayaranIurans\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class PembayaranIuranForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('iuran_warga_id')
                    ->required()
                    ->numeric(),
                TextInput::make('transaksi_id')
                    ->required()
                    ->numeric(),
                DatePicker::make('tanggal_bayar')
                    ->required(),
                TextInput::make('jumlah')
                    ->required()
                    ->numeric(),
                TextInput::make('metode_pembayaran')
                    ->required(),
                Textarea::make('keterangan')
                    ->columnSpanFull(),
            ]);
    }
}
