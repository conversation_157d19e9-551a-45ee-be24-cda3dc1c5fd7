<template>
    <section class="xs:px-6 relative z-1 px-3">
        <div
            class="xs:grid-cols-4 mb-4 grid grid-cols-3 gap-4 rounded-2xl border border-white/70 bg-white/30 py-4 backdrop-blur-sm dark:border-black/20 dark:bg-black/20"
        >
            <div
                v-for="(item, index) in menuHighlight"
                :key="index"
                class="flex items-start justify-center transition-all duration-300"
                data-aos="fade-up"
            >
                <div class="group flex flex-col items-center justify-center text-center text-gray-600 hover:text-blue-500">
                    <div
                        class="border-gradient rounded-2xl bg-white/50 p-3.5 text-blue-400 backdrop-blur-sm group-hover:bg-white/70 dark:bg-black/20 dark:group-hover:bg-black/70"
                    >
                        <Icon :icon="item.icon" class="size-8 sm:size-10" />
                    </div>
                    <span class="mt-2 text-sm leading-tight text-black dark:text-white">{{ item.label }}</span>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
const menuHighlight = [
    { to: '/#', label: 'Kas', icon: 'solar:pie-chart-2-bold-duotone' },
    { to: '/#', label: 'Laporan', icon: 'solar:document-add-bold-duotone' },
    { to: '/#', label: 'Informasi', icon: 'solar:volume-bold-duotone' },
];
</script>
