<?php

namespace App\Filament\Resources\IuranWargas;

use App\Filament\Resources\IuranWargas\Pages\CreateIuranWarga;
use App\Filament\Resources\IuranWargas\Pages\EditIuranWarga;
use App\Filament\Resources\IuranWargas\Pages\ListIuranWargas;
use App\Filament\Resources\IuranWargas\Schemas\IuranWargaForm;
use App\Filament\Resources\IuranWargas\Tables\IuranWargasTable;
use App\Models\IuranWarga;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class IuranWargaResource extends Resource
{
    protected static ?string $model = IuranWarga::class;

    protected static ?string $navigationLabel = 'Iuran Warga';
    protected static string|BackedEnum|null $navigationIcon = Heroicon::UserGroup;

    public static function form(Schema $schema): Schema
    {
        return IuranWargaForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return IuranWargasTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListIuranWargas::route('/'),
            'create' => CreateIuranWarga::route('/create'),
            'edit' => EditIuranWarga::route('/{record}/edit'),
        ];
    }
}
