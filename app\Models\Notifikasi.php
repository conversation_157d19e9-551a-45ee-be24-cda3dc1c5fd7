<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notifikasi extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'dibaca' => 'boolean',
    ];

    // <PERSON><PERSON>i ke User
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scope untuk notifikasi belum dibaca
    public function scopeUnread($query)
    {
        return $query->where('dibaca', false);
    }

    // Mark as read
    public function markAsRead()
    {
        $this->update(['dibaca' => true]);
    }
}
