<template>
  <section class="relative z-1 px-6">
    <div class="text-lg font-bold py-3" data-aos="fade-up">Pemerintahan</div>
    <div class="grid grid-cols-4 gap-4 mb-4">
      <div
          v-for="(item, index) in menuPemerintahan"
          :key="item.to"
          class="flex items-start justify-center transition-all duration-300"
          data-aos="fade-up"
      >
        <div
            class="flex items-center justify-center flex-col  text-gray-600 group hover:text-indigo-500 max-w-20 text-center"
        >
          <div class="bg-white/50 group-hover:bg-indigo-500/50 group-hover:text-white rounded-2xl p-3.5">
            <component :is="item.icon" class="size-8"/>
          </div>
          <span class="text-[13px] leading-tight mt-2">{{ item.label }}</span>
        </div>
      </div>
    </div>
    <div class="text-lg font-bold py-3" data-aos="fade-up">Ruang Warga</div>
    <div class="grid grid-cols-4 gap-4 mb-4">
      <div
          v-for="(item, index) in menuRuangWarga"
          :key="item.to"
          class="flex items-start justify-center transition-all duration-300"
          data-aos="fade-up"
      >
        <div
            class="flex items-center justify-center flex-col  text-gray-600 group hover:text-indigo-500 max-w-20 text-center"
        >
          <div class="bg-white/50 group-hover:bg-indigo-500/50 group-hover:text-white rounded-2xl p-3.5">
            <component :is="item.icon" class="size-8"/>
          </div>
          <span class="text-[13px] leading-tight mt-2">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import {
  Home,
  Calendar,
  Star,
  ChartPie,
  Users,
  ReceiptText,
  FilePlus,
  MessageSquareWarning,
  CircleAlert,
  ShoppingBag,
  Umbrella,
} from "lucide-vue-next";
import { Link } from '@inertiajs/vue3';


const menuPemerintahan = [
  {to: "/#", label: "Anggaran & Program", icon: ChartPie},
  {to: "/#", label: "Aparatur RT", icon: Users},
  {to: "/#", label: "Informasi Tender", icon: ReceiptText},
];
const menuRuangWarga = [
  {to: "/#", label: "Pembuatan Dokumen", icon: FilePlus},
  {to: "/#", label: "Pengajuan Kegiatan", icon: Calendar},
  {to: "/#", label: "Saran & Pengaduan", icon: MessageSquareWarning},
  {to: "/#", label: "Rating Aparatur", icon: Star},
  {to: "/#", label: "Alert & Pemberitahuan", icon: CircleAlert},
  {to: "/#", label: "Umkm", icon: ShoppingBag},
  {to: "/#", label: "Cek BPJS", icon: Umbrella},
];
</script>
