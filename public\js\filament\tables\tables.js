(()=>{var h=({canSelectMultipleRecords:l,canTrackDeselectedRecords:a,currentSelectionLivewireProperty:i,$wire:t})=>({checkboxClickController:null,collapsedGroups:[],isLoading:!1,selectedRecords:new Set,deselectedRecords:new Set,isTrackingDeselectedRecords:!1,shouldCheckUniqueSelection:!0,lastCheckedRecord:null,livewireId:null,entangledSelectedRecords:i?t.$entangle(i):null,init(){this.livewireId=this.$root.closest("[wire\\:id]").attributes["wire:id"].value,t.$on("deselectAllTableRecords",()=>this.deselectAllRecords()),i&&(l?this.selectedRecords=new Set(this.entangledSelectedRecords):this.selectedRecords=new Set(this.entangledSelectedRecords?[this.entangledSelectedRecords]:[])),this.$nextTick(()=>this.watchForCheckboxClicks()),Livewire.hook("element.init",({component:e})=>{e.id===this.livewireId&&this.watchForCheckboxClicks()})},mountAction(...e){t.set("isTrackingDeselectedTableRecords",this.isTrackingDeselectedRecords,!1),t.set("selectedTableRecords",[...this.selectedRecords],!1),t.set("deselectedTableRecords",[...this.deselectedRecords],!1),t.mountAction(...e)},toggleSelectRecordsOnPage(){let e=this.getRecordsOnPage();if(this.areRecordsSelected(e)){this.deselectRecords(e);return}this.selectRecords(e)},async toggleSelectRecordsInGroup(e){if(this.isLoading=!0,this.areRecordsSelected(this.getRecordsInGroupOnPage(e))){this.deselectRecords(await t.getGroupedSelectableTableRecordKeys(e));return}this.selectRecords(await t.getGroupedSelectableTableRecordKeys(e)),this.isLoading=!1},getRecordsInGroupOnPage(e){let s=[];for(let c of this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[])c.dataset.group===e&&s.push(c.value);return s},getSelectedRecordsCount(){return this.isTrackingDeselectedRecords?(this.$refs.allSelectableRecordsCount?.value??this.deselectedRecords.size)-this.deselectedRecords.size:this.selectedRecords.size},getRecordsOnPage(){let e=[];for(let s of this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[])e.push(s.value);return e},selectRecords(e){l||(this.deselectAllRecords(),e=e.slice(0,1));for(let s of e)if(!this.isRecordSelected(s)){if(this.isTrackingDeselectedRecords){this.deselectedRecords.delete(s);continue}this.selectedRecords.add(s)}this.updatedSelectedRecords()},deselectRecords(e){for(let s of e){if(this.isTrackingDeselectedRecords){this.deselectedRecords.add(s);continue}this.selectedRecords.delete(s)}this.updatedSelectedRecords()},updatedSelectedRecords(){if(l){this.entangledSelectedRecords=[...this.selectedRecords];return}this.entangledSelectedRecords=[...this.selectedRecords][0]??null},toggleSelectedRecord(e){if(this.isRecordSelected(e)){this.deselectRecords([e]);return}this.selectRecords([e])},async selectAllRecords(){if(!a){this.isLoading=!0,this.selectedRecords=new Set(await t.getAllSelectableTableRecordKeys()),this.updatedSelectedRecords(),this.isLoading=!1;return}this.isTrackingDeselectedRecords=!0,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},deselectAllRecords(){this.isTrackingDeselectedRecords=!1,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},isRecordSelected(e){return this.isTrackingDeselectedRecords?!this.deselectedRecords.has(e):this.selectedRecords.has(e)},areRecordsSelected(e){return e.every(s=>this.isRecordSelected(s))},toggleCollapseGroup(e){if(this.isGroupCollapsed(e)){this.collapsedGroups.splice(this.collapsedGroups.indexOf(e),1);return}this.collapsedGroups.push(e)},isGroupCollapsed(e){return this.collapsedGroups.includes(e)},resetCollapsedGroups(){this.collapsedGroups=[]},watchForCheckboxClicks(){this.checkboxClickController&&this.checkboxClickController.abort(),this.checkboxClickController=new AbortController;let{signal:e}=this.checkboxClickController;this.$root?.addEventListener("click",s=>s.target?.matches(".fi-ta-record-checkbox")&&this.handleCheckboxClick(s,s.target),{signal:e})},handleCheckboxClick(e,s){if(!this.lastChecked){this.lastChecked=s;return}if(e.shiftKey){let c=Array.from(this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[]);if(!c.includes(this.lastChecked)){this.lastChecked=s;return}let n=c.indexOf(this.lastChecked),R=c.indexOf(s),r=[n,R].sort((d,u)=>d-u),o=[];for(let d=r[0];d<=r[1];d++)c[d].checked=s.checked,o.push(c[d].value);s.checked?this.selectRecords(o):this.deselectRecords(o)}this.lastChecked=s}});document.addEventListener("alpine:init",()=>{window.Alpine.data("filamentTable",h)});})();
