<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LaporanKeuangan extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'periode_awal' => 'date',
        'periode_akhir' => 'date',
    ];

    // <PERSON><PERSON>i ke User yang membuat laporan
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Accessor untuk jenis laporan label
    public function getJenisLaporanLabelAttribute()
    {
        $labels = [
            'neraca' => 'Neraca',
            'laba_rugi' => 'Laba Rugi',
            'arus_kas' => 'Arus Kas',
            'perubahan_modal' => 'Perubahan Modal'
        ];

        return $labels[$this->jenis_laporan] ?? $this->jenis_laporan;
    }

    // Accessor untuk periode format
    public function getPeriodeAttribute()
    {
        return $this->periode_awal->format('d M Y') . ' - ' . $this->periode_akhir->format('d M Y');
    }
}
