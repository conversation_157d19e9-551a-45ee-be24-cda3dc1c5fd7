<?php

namespace App\Http\Controllers;

use App\Models\JenisIuran;
use Illuminate\Http\Request;

class JenisIuranController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(JenisIuran $jenisIuran)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(JenisIuran $jenisIuran)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, JenisIuran $jenisIuran)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JenisIuran $jenisIuran)
    {
        //
    }
}
