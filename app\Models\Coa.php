<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coa extends Model
{
    use HasFactory;

    protected $guarded = [];

    // Relasi ke parent (untuk akun header)
    public function parent()
    {
        return $this->belongsTo(Coa::class, 'parent_id');
    }

    // Relasi ke children (untuk akun detail)
    public function children()
    {
        return $this->hasMany(Coa::class, 'parent_id');
    }

    // Relasi ke Jurnal
    public function jurnal()
    {
        return $this->hasMany(Jurnal::class);
    }

    // Relasi ke Saldo
    public function saldo()
    {
        return $this->hasMany(Saldo::class);
    }

    // Cek apakah akun header
    public function isHeader()
    {
        return $this->tipe === 'header';
    }

    // Cek apakah akun detail
    public function isDetail()
    {
        return $this->tipe === 'detail';
    }
}
