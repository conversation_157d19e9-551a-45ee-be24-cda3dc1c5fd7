<?php

namespace App\Filament\Resources\Wargas\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class WargaForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('user_id')
                    ->numeric(),
                TextInput::make('nik')
                    ->required(),
                TextInput::make('nama_lengkap')
                    ->required(),
                TextInput::make('tempat_lahir')
                    ->required(),
                DatePicker::make('tanggal_lahir')
                    ->required(),
                Select::make('jenis_kelamin')
                    ->options(['L' => 'L', 'P' => 'P'])
                    ->required(),
                Select::make('status_perkawinan')
                    ->options([
            'belum_kawin' => 'Belum kawin',
            'kawin' => 'Kawin',
            'cerai_hidup' => 'Cerai hidup',
            'cerai_mati' => 'Cerai mati',
        ])
                    ->required(),
                TextInput::make('agama')
                    ->required(),
                TextInput::make('pendidikan_terakhir')
                    ->required(),
                TextInput::make('pekerjaan')
                    ->required(),
                TextInput::make('alamat')
                    ->required(),
                TextInput::make('rt')
                    ->required(),
                TextInput::make('rw')
                    ->required(),
                TextInput::make('kelurahan')
                    ->required(),
                TextInput::make('kecamatan')
                    ->required(),
                TextInput::make('kota')
                    ->required(),
                TextInput::make('provinsi')
                    ->required(),
                TextInput::make('kode_pos')
                    ->required(),
                TextInput::make('no_telepon')
                    ->tel()
                    ->required(),
                Select::make('status_warga')
                    ->options(['aktif' => 'Aktif', 'meninggal' => 'Meninggal', 'pindah' => 'Pindah'])
                    ->default('aktif')
                    ->required(),
                TextInput::make('foto_ktp'),
            ]);
    }
}
