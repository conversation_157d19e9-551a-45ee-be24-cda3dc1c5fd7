<?php

namespace Database\Seeders;

use App\Models\KategoriLaporan;
use Illuminate\Database\Seeder;

class KategoriLaporanSeeder extends Seeder
{
    public function run()
    {
        KategoriLaporan::truncate();

        $kategories = [
            ['nama' => 'Infrastruktur', 'deskripsi' => 'Laporan terkait infrastruktur RT'],
            ['nama' => 'Keamanan', 'deskripsi' => 'Laporan terkait keamanan lingkungan'],
            ['nama' => 'Kebersihan', 'deskripsi' => 'Laporan terkait kebersihan lingkungan'],
            ['nama' => 'Sosial', 'deskripsi' => 'Laporan terkait masalah sosial'],
            ['nama' => 'Lainnya', 'deskripsi' => 'Laporan lainnya'],
        ];

        foreach ($kategories as $kategori) {
            KategoriLaporan::create($kategori);
        }
    }
}
