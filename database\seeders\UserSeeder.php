<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run()
    {
        User::truncate();

        $users = [
            [
                'name' => 'Admin RT',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
            ],
            [
                'name' => 'Bendahara RT',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'bendahara',
            ],
            [
                'name' => 'Ketua RT',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'ketua',
            ],
            [
                'name' => 'Warga Contoh',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'warga',
            ],
        ];

        foreach ($users as $user) {
            User::create($user);
        }

        // Generate 20 warga biasa
        User::factory()->count(20)->create(['role' => 'warga']);
    }
}
