<template>
    <div class="mb-24">
        <div class="flex items-center justify-between px-3 xs:px-6">
            <div class="text-xl font-bold">La<PERSON>an</div>
            <div class="font-bold">
                <MoveRight class="text-xl" />
            </div>
        </div>
        <Swiper />
    </div>
</template>

<script setup lang="ts">
import Swiper from '@/components/swiper.vue';
import { MoveRight } from 'lucide-vue-next';
</script>
