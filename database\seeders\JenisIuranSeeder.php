<?php

namespace Database\Seeders;

use App\Models\JenisIuran;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JenisIuranSeeder extends Seeder
{
    public function run()
    {
        JenisIuran::truncate();

        $jenisIurans = [
            [
                'nama' => 'Iuran Kebersihan',
                'periode' => 'bulanan',
                'jumlah' => 20000,
                'keterangan' => 'Iuran untuk kebersihan lingkungan RT',
            ],
            [
                'nama' => 'Iuran Keamanan',
                'periode' => 'bulanan',
                'jumlah' => 15000,
                'keterangan' => 'Iuran untuk keamanan lingkungan RT',
            ],
            [
                'nama' => 'Iuran Kegiatan',
                'periode' => 'sekali',
                'jumlah' => 50000,
                'keterangan' => 'Iuran untuk kegiatan RT',
            ],
            [
                'nama' => 'Iuran Sampah',
                'periode' => 'bulanan',
                'jumlah' => 10000,
                'keterangan' => 'Iuran untuk pengelolaan sampah',
            ],
        ];

        foreach ($jenisIurans as $jenisIuran) {
            JenisIuran::create($jenisIuran);
        }
    }
}
