<?php

namespace App\Filament\Resources\LaporanWargas\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class LaporanWargaForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('warga_id')
                    ->required()
                    ->numeric(),
                TextInput::make('kategori_id')
                    ->required()
                    ->numeric(),
                TextInput::make('judul')
                    ->required(),
                Textarea::make('deskripsi')
                    ->required()
                    ->columnSpanFull(),
                Select::make('status')
                    ->options([
            'diterima' => 'Diterima',
            'diproses' => 'Diproses',
            'selesai' => 'Selesai',
            'ditolak' => 'Ditolak',
        ])
                    ->default('diterima')
                    ->required(),
                Textarea::make('respon')
                    ->columnSpanFull(),
                TextInput::make('lampiran'),
            ]);
    }
}
