<?php

namespace App\Filament\Resources\KategoriLaporans;

use App\Filament\Resources\KategoriLaporans\Pages\CreateKategoriLaporan;
use App\Filament\Resources\KategoriLaporans\Pages\EditKategoriLaporan;
use App\Filament\Resources\KategoriLaporans\Pages\ListKategoriLaporans;
use App\Filament\Resources\KategoriLaporans\Schemas\KategoriLaporanForm;
use App\Filament\Resources\KategoriLaporans\Tables\KategoriLaporansTable;
use App\Models\KategoriLaporan;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class KategoriLaporanResource extends Resource
{
    protected static ?string $model = KategoriLaporan::class;

    protected static ?string $navigationLabel = 'Laporan Warga';

    protected static string|BackedEnum|null $navigationIcon = Heroicon::Flag;

    public static function form(Schema $schema): Schema
    {
        return KategoriLaporanForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return KategoriLaporansTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListKategoriLaporans::route('/'),
            'create' => CreateKategoriLaporan::route('/create'),
            'edit' => EditKategoriLaporan::route('/{record}/edit'),
        ];
    }
}
