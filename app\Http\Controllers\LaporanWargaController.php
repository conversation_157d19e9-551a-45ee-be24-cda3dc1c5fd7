<?php

namespace App\Http\Controllers;

use App\Models\LaporanWarga;
use Illuminate\Http\Request;

class LaporanWargaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(LaporanWarga $laporanWarga)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LaporanWarga $laporanWarga)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LaporanWarga $laporanWarga)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LaporanWarga $laporanWarga)
    {
        //
    }
}
