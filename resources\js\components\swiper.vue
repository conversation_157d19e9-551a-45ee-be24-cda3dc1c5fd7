<template>
    <div class="-mx-4 relative">
        <div class="absolute left-0 h-full w-7 bg-gradient-to-r from-[#eaf2ff] dark:from-neutral-800 to-transparent z-3"></div>
        <div class="absolute right-0 h-full w-7 bg-gradient-to-r to-[#eaf2ff] dark:to-neutral-800 from-transparent z-3"></div>
        <swiper
            :modules="[EffectCreative]"
            :breakpoints="{
                0: { slidesPerView: 1.5, spaceBetween: 20 },
                480: { slidesPerView: 1.8, spaceBetween: 20 },
                640: { slidesPerView: 2.5, spaceBetween: 20 },
                // 768: { slidesPerView: 3, spaceBetween: 20 },
            }"
            :navigation="false"
            :pagination="{ clickable: true }"
            class="mySwiper"
        >
            <swiper-slide v-for="(slide, index) in slides" :key="index" class="py-3">
                <div
                    class="mb-4 flex flex-col justify-between gap-2 min-h-48 p-3 rounded-xl bg-white/50 dark:bg-black/50 backdrop-blur-sm border-gradient"
                    style="box-shadow: 8px 8px 15px rgba(0, 0, 0, 0.15)"
                >
                    <div class="w-full border border-white dark:border-neutral-800 rounded-lg h-36 flex justify-center items-center">
                        <Image class="size-20 text-gray-200 dark:text-gray-700"/>
                    </div>
                    <div>
                        <p class="text-sm font-medium line-clamp-2">{{ slide.title }} Lorem ipsum dolor sit amet, consectetur adipisicing elit. Cupiditate fugiat natus praesentium!</p>
                    </div>
                </div>
            </swiper-slide>
        </swiper>
    </div>
</template>

<script setup lang="ts">
import { EffectCreative } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Image } from 'lucide-vue-next';

import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const slides: { title: string; img: string }[] = [
    {
        title: 'Slide 1',
        img: 'slide',
    },
    {
        title: 'Slide 1',
        img: 'slide',
    },
    {
        title: 'Slide 1',
        img: 'slide',
    },
    {
        title: 'Slide 1',
        img: 'slide',
    },
    {
        title: 'Slide 1',
        img: 'slide',
    },
];
</script>

<style scoped>
.mySwiper {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    box-sizing: content-box;
}
@media screen and (max-width: 30rem){
    .mySwiper {
        padding-left: 1.7rem;
        padding-right: 1.7rem;
        box-sizing: content-box;
    }
}
</style>
