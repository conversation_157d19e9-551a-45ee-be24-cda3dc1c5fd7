function u({activeTab:i,isTabPersistedInQueryString:e,livewireId:a,tab:r,tabQueryStringKey:n}){return{tab:r,init(){this.$watch("tab",()=>this.updateQueryString());let t=this.getTabs();(!this.tab||!t.includes(this.tab))&&(this.tab=t[i-1]),Livewire.hook("commit",({component:h,commit:b,succeed:o,fail:c,respond:f})=>{o(({snapshot:l,effect:d})=>{this.$nextTick(()=>{if(h.id!==a)return;let s=this.getTabs();s.includes(this.tab)||(this.tab=s[i-1]??this.tab)})})})},getTabs(){return this.$refs.tabsData?JSON.parse(this.$refs.tabsData.value):[]},updateQueryString(){if(!e)return;let t=new URL(window.location.href);t.searchParams.set(n,this.tab),history.replaceState(null,document.title,t.toString())}}}export{u as default};
