<script setup lang="ts">
import { onMounted, onBeforeUnmount } from 'vue'
import { useAppearance } from '@/composables/useAppearance'
import { Monitor, Moon, Sun } from 'lucide-vue-next'

const { appearance, updateAppearance } = useAppearance()

const tabs = [
    { value: 'light', Icon: Sun, label: 'Light' },
    { value: 'dark', Icon: Moon, label: 'Dark' },
    { value: 'system', Icon: Monitor, label: 'System' },
] as const

function handleKeydown(e: KeyboardEvent) {
    const key = e.key.toLowerCase()
    const isCmdOrCtrl = e.metaKey || e.ctrlKey
    const isAlt = e.altKey

    // Deteksi kombinasi dan eksekusi
    if (isCmdOrCtrl && isAlt) {
        if (['l', 'd', 's'].includes(key)) {
            e.preventDefault() // blokir default browser action
            if (key === 'l') updateAppearance('light')
            if (key === 'd') updateAppearance('dark')
            if (key === 's') updateAppearance('system')
        }
    }
}

onMounted(() => {
    window.addEventListener('keydown', handleKeydown)
})

onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeydown)
})
</script>


<template>
    <div class="inline-flex gap-1 rounded-md bg-neutral-100 p-1 dark:bg-neutral-800 w-full">
        <button
            v-for="{ value, Icon } in tabs"
            :key="value"
            @click="updateAppearance(value)"
            :class="[
                'flex items-center justify-center rounded-sm px-2 py-2 transition-colors w-full',
                appearance === value
                    ? 'bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100'
                    : 'text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60',
            ]"
        >
            <component :is="Icon" class="h-4 w-4" />
        </button>
    </div>
</template>
