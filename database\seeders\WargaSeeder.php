<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Warga;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class WargaSeeder extends Seeder
{
    public function run()
    {
        Warga::truncate();

        $users = User::where('role', 'warga')->get();

        foreach ($users as $user) {
            Warga::create([
                'user_id' => $user->id,
                'nik' => strval(mt_rand(1000000000000000, 9999999999999999)), // 16 digit
                'nama_lengkap' => $user->name,
                'tempat_lahir' => '<PERSON> Contoh',
                'tanggal_lahir' => Carbon::now()->subYears(rand(20, 60))->subMonths(rand(1, 12))->subDays(rand(1, 30)),
                'jenis_kelamin' => rand(0, 1) ? 'L' : 'P',
                'status_perkawinan' => ['belum_kawin', 'kawin', 'cerai_hidup', 'cerai_mati'][rand(0, 3)],
                'agama' => ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', 'Hindu', '<PERSON>', '<PERSON><PERSON><PERSON>'][rand(0, 5)],
                'pendidikan_terakhir' => ['SD', 'SMP', 'SMA', 'D3', 'S1', 'S2'][rand(0, 5)],
                'pekerjaan' => ['Wiraswasta', 'PNS', 'Karyawan Swasta', 'Petani', 'Nelayan', 'Ibu Rumah Tangga'][rand(0, 5)],
                'alamat' => 'Jl. Contoh No.' . rand(1, 100),
                'rt' => '01',
                'rw' => '05',
                'kelurahan' => 'Kelurahan Contoh',
                'kecamatan' => 'Kecamatan Contoh',
                'kota' => 'Kota Contoh',
                'provinsi' => 'Provinsi Contoh',
                'kode_pos' => '12345',
                'no_telepon' => '08' . rand(100000000, 999999999),
                'status_warga' => 'aktif',
            ]);
        }

        // Tambahkan data warga tanpa user (contoh keluarga yang belum memiliki akun)
        Warga::factory()->count(50)->create();
    }
}
