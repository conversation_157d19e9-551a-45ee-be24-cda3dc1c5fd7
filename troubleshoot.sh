#!/bin/bash

echo "=== Laravel + Inertia + Vue Debugging ==="
echo ""

# Check if containers are running
echo "1. Checking running containers:"
docker-compose ps
echo ""

# Check node container logs
echo "2. Node container logs:"
docker-compose logs node
echo ""

# Check if Vite is running and accessible
echo "3. Testing Vite dev server connection:"
curl -s http://localhost:5173 || echo "Vite dev server not accessible"
echo ""

# Check if node_modules exists and has required packages
echo "4. Checking node_modules in container:"
docker-compose exec node ls -la /rtapp/node_modules/.bin/ | grep vite || echo "Vite not found in node_modules"
echo ""

# Check package.json scripts
echo "5. Checking package.json scripts:"
docker-compose exec node cat /rtapp/package.json | grep -A 10 '"scripts"'
echo ""

# Check Vite config
echo "6. Checking Vite config:"
docker-compose exec node cat /rtapp/vite.config.js 2>/dev/null || echo "vite.config.js not found"
echo ""

# Check if Vue files exist
echo "7. Checking Vue files structure:"
docker-compose exec node find /rtapp/resources/js -name "*.vue" | head -10
echo ""

# Test npm run dev manually
echo "8. Testing npm run dev manually:"
docker-compose exec node sh -c "cd /rtapp && npm run dev --verbose" &
sleep 5
kill %1 2>/dev/null
echo ""

echo "=== Debug Complete ==="
