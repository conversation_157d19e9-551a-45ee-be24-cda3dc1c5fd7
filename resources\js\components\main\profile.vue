<template>
    <div class="relative z-2 flex items-center space-x-6 xs:space-x-4" data-aos="fade-down">

        <Avatar class="h-12 w-12 overflow-hidden rounded-full">
            <AvatarImage v-if="showAvatar" :src="user.avatar" :alt="user.name" />
            <AvatarFallback class="rounded-full text-black dark:text-white">
                {{ getInitials(user.name) }}
            </AvatarFallback>
        </Avatar>

        <div class="flex flex-col">
            <p class="text-lg font-semibold text-white">Hi, {{ user.name }}</p>
            <p class="text-xs text-white">{{ user.email }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { User } from '@/types';
import { useInitials } from '@/composables/useInitials';
import { computed } from 'vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface Props {
    user: User;
}

const props = defineProps<Props>();

const { getInitials } = useInitials();
const showAvatar = computed(() => props.user.avatar && props.user.avatar !== '');
</script>
