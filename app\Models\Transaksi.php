<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaksi extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'tanggal' => 'date',
    ];

    // <PERSON><PERSON>i ke User yang membuat transaksi
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relasi ke Jurnal
    public function jurnal()
    {
        return $this->hasMany(Jurnal::class);
    }

    // Relasi ke Pembayaran Iuran (jika transaksi untuk pembayaran iuran)
    public function pembayaranIuran()
    {
        return $this->hasOne(PembayaranIuran::class);
    }

    // Hitung total debit
    public function totalDebit()
    {
        return $this->jurnal->sum('debit');
    }

    // Hitung total kredit
    public function totalKredit()
    {
        return $this->jurnal->sum('kredit');
    }
}
