services:
  web:
    build:
      args:
        INSTALL_DEV: "false"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false

  node:
    # Override command untuk production - build lalu cleanup
    command: sh -c "npm ci --only=production && npm run build && echo 'Build completed, cleaning up...' && rm -rf node_modules && npm cache clean --force"
    # Hapus port mapping karena tidak perlu dev server di production
    ports: []
    environment:
      - NODE_ENV=production
    # Volume hanya untuk build output
    volumes:
      - build_output:/kampung_project/public/build
    # Restart policy none karena container akan exit setelah build
    restart: "no"

volumes:
  build_output:
