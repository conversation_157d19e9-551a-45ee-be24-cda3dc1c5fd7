<?php

namespace Database\Seeders;

use App\Models\Coa;
use App\Models\IuranWarga;
use App\Models\JenisIuran;
use App\Models\Keluarga;
use App\Models\Transaksi;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IuranWargaSeeder extends Seeder
{
    public function run()
    {
        IuranWarga::truncate();
        // Juga truncate tabel terkait pembayaran jika perlu
        DB::table('pembayaran_iurans')->truncate();

        // Hapus transaksi iuran yang sudah ada
        Transaksi::where('jenis', 'pemasukan')->delete();

        $jenisIurans = JenisIuran::all();
        $keluargas = Keluarga::all();
        $now = Carbon::now();
        $bendahara = User::where('role', 'bendahara')->first();

        foreach ($keluargas as $keluarga) {
            foreach ($jenisIurans as $jenisIuran) {
                $status = rand(0, 1) ? 'lunas' : 'belum_lunas';

                $iuran = IuranWarga::create([
                    'jenis_iuran_id' => $jenisIuran->id,
                    'keluarga_id' => $keluarga->id,
                    'tanggal_jatuh_tempo' => $now->copy()->subMonths(rand(1, 3)),
                    'status' => $status,
                ]);

                // Jika lunas, buat pembayaran
                if ($status === 'lunas') {
                    $this->createPembayaran($iuran, $bendahara);
                }
            }
        }
    }

    protected function createPembayaran($iuran, $user)
    {
        // Gunakan uniqid() atau kombinasi yang lebih unik
        $kodeTransaksi = 'TRX-' . time() . '-' . $iuran->id . '-' . rand(1000, 9999);

        $transaksi = Transaksi::create([
            'kode_transaksi' => $kodeTransaksi,
            'tanggal' => $iuran->tanggal_jatuh_tempo->copy()->addDays(rand(0, 5)),
            'keterangan' => 'Pembayaran ' . $iuran->jenisIuran->nama . ' Keluarga ' . $iuran->keluarga->no_kk,
            'jenis' => 'pemasukan',
            'user_id' => $user->id,
        ]);

        // Jurnal untuk Kas
        $transaksi->jurnal()->create([
            'coa_id' => Coa::where('kode', '1.1')->first()->id,
            'debit' => $iuran->jenisIuran->jumlah,
            'kredit' => 0,
            'keterangan' => 'Pembayaran iuran',
        ]);

        // Jurnal untuk Pendapatan Iuran
        $transaksi->jurnal()->create([
            'coa_id' => Coa::where('kode', '4.1')->first()->id,
            'debit' => 0,
            'kredit' => $iuran->jenisIuran->jumlah,
            'keterangan' => 'Pendapatan iuran',
        ]);

        $iuran->pembayaran()->create([
            'transaksi_id' => $transaksi->id,
            'tanggal_bayar' => $transaksi->tanggal,
            'jumlah' => $iuran->jenisIuran->jumlah,
            'metode_pembayaran' => ['tunai', 'transfer'][rand(0, 1)],
            'keterangan' => 'Lunas',
        ]);
    }
}
