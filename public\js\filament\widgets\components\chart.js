var Zc=Object.defineProperty;var qc=(s,t,e)=>t in s?Zc(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var S=(s,t,e)=>qc(s,typeof t!="symbol"?t+"":t,e);function ks(s){return s+.5|0}var te=(s,t,e)=>Math.max(Math.min(s,e),t);function ws(s){return te(ks(s*2.55),0,255)}function ee(s){return te(ks(s*255),0,255)}function Bt(s){return te(ks(s/2.55)/100,0,1)}function Mr(s){return te(ks(s*100),0,100)}var _t={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Tn=[..."0123456789ABCDEF"],Gc=s=>Tn[s&15],Xc=s=>Tn[(s&240)>>4]+Tn[s&15],vi=s=>(s&240)>>4===(s&15),Kc=s=>vi(s.r)&&vi(s.g)&&vi(s.b)&&vi(s.a);function Jc(s){var t=s.length,e;return s[0]==="#"&&(t===4||t===5?e={r:255&_t[s[1]]*17,g:255&_t[s[2]]*17,b:255&_t[s[3]]*17,a:t===5?_t[s[4]]*17:255}:(t===7||t===9)&&(e={r:_t[s[1]]<<4|_t[s[2]],g:_t[s[3]]<<4|_t[s[4]],b:_t[s[5]]<<4|_t[s[6]],a:t===9?_t[s[7]]<<4|_t[s[8]]:255})),e}var Qc=(s,t)=>s<255?t(s):"";function th(s){var t=Kc(s)?Gc:Xc;return s?"#"+t(s.r)+t(s.g)+t(s.b)+Qc(s.a,t):void 0}var eh=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Dr(s,t,e){let i=t*Math.min(e,1-e),n=(o,r=(o+s/30)%12)=>e-i*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function sh(s,t,e){let i=(n,o=(n+s/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[i(5),i(3),i(1)]}function ih(s,t,e){let i=Dr(s,1,.5),n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)i[n]*=1-t-e,i[n]+=t;return i}function nh(s,t,e,i,n){return s===n?(t-e)/i+(t<e?6:0):t===n?(e-s)/i+2:(s-t)/i+4}function On(s){let e=s.r/255,i=s.g/255,n=s.b/255,o=Math.max(e,i,n),r=Math.min(e,i,n),a=(o+r)/2,l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=nh(e,i,n,h,o),l=l*60+.5),[l|0,c||0,a]}function Dn(s,t,e,i){return(Array.isArray(t)?s(t[0],t[1],t[2]):s(t,e,i)).map(ee)}function En(s,t,e){return Dn(Dr,s,t,e)}function oh(s,t,e){return Dn(ih,s,t,e)}function rh(s,t,e){return Dn(sh,s,t,e)}function Er(s){return(s%360+360)%360}function ah(s){let t=eh.exec(s),e=255,i;if(!t)return;t[5]!==i&&(e=t[6]?ws(+t[5]):ee(+t[5]));let n=Er(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?i=oh(n,o,r):t[1]==="hsv"?i=rh(n,o,r):i=En(n,o,r),{r:i[0],g:i[1],b:i[2],a:e}}function lh(s,t){var e=On(s);e[0]=Er(e[0]+t),e=En(e),s.r=e[0],s.g=e[1],s.b=e[2]}function ch(s){if(!s)return;let t=On(s),e=t[0],i=Mr(t[1]),n=Mr(t[2]);return s.a<255?`hsla(${e}, ${i}%, ${n}%, ${Bt(s.a)})`:`hsl(${e}, ${i}%, ${n}%)`}var vr={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Tr={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function hh(){let s={},t=Object.keys(Tr),e=Object.keys(vr),i,n,o,r,a;for(i=0;i<t.length;i++){for(r=a=t[i],n=0;n<e.length;n++)o=e[n],a=a.replace(o,vr[o]);o=parseInt(Tr[r],16),s[a]=[o>>16&255,o>>8&255,o&255]}return s}var Ti;function uh(s){Ti||(Ti=hh(),Ti.transparent=[0,0,0,0]);let t=Ti[s.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}var dh=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function fh(s){let t=dh.exec(s),e=255,i,n,o;if(t){if(t[7]!==i){let r=+t[7];e=t[8]?ws(r):te(r*255,0,255)}return i=+t[1],n=+t[3],o=+t[5],i=255&(t[2]?ws(i):te(i,0,255)),n=255&(t[4]?ws(n):te(n,0,255)),o=255&(t[6]?ws(o):te(o,0,255)),{r:i,g:n,b:o,a:e}}}function mh(s){return s&&(s.a<255?`rgba(${s.r}, ${s.g}, ${s.b}, ${Bt(s.a)})`:`rgb(${s.r}, ${s.g}, ${s.b})`)}var vn=s=>s<=.0031308?s*12.92:Math.pow(s,1/2.4)*1.055-.055,Re=s=>s<=.04045?s/12.92:Math.pow((s+.055)/1.055,2.4);function gh(s,t,e){let i=Re(Bt(s.r)),n=Re(Bt(s.g)),o=Re(Bt(s.b));return{r:ee(vn(i+e*(Re(Bt(t.r))-i))),g:ee(vn(n+e*(Re(Bt(t.g))-n))),b:ee(vn(o+e*(Re(Bt(t.b))-o))),a:s.a+e*(t.a-s.a)}}function Oi(s,t,e){if(s){let i=On(s);i[t]=Math.max(0,Math.min(i[t]+i[t]*e,t===0?360:1)),i=En(i),s.r=i[0],s.g=i[1],s.b=i[2]}}function Ir(s,t){return s&&Object.assign(t||{},s)}function Or(s){var t={r:0,g:0,b:0,a:255};return Array.isArray(s)?s.length>=3&&(t={r:s[0],g:s[1],b:s[2],a:255},s.length>3&&(t.a=ee(s[3]))):(t=Ir(s,{r:0,g:0,b:0,a:1}),t.a=ee(t.a)),t}function ph(s){return s.charAt(0)==="r"?fh(s):ah(s)}var Ss=class s{constructor(t){if(t instanceof s)return t;let e=typeof t,i;e==="object"?i=Or(t):e==="string"&&(i=Jc(t)||uh(t)||ph(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=Ir(this._rgb);return t&&(t.a=Bt(t.a)),t}set rgb(t){this._rgb=Or(t)}rgbString(){return this._valid?mh(this._rgb):void 0}hexString(){return this._valid?th(this._rgb):void 0}hslString(){return this._valid?ch(this._rgb):void 0}mix(t,e){if(t){let i=this.rgb,n=t.rgb,o,r=e===o?.5:e,a=2*r-1,l=i.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,i.r=255&c*i.r+o*n.r+.5,i.g=255&c*i.g+o*n.g+.5,i.b=255&c*i.b+o*n.b+.5,i.a=r*i.a+(1-r)*n.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=gh(this._rgb,t._rgb,e)),this}clone(){return new s(this.rgb)}alpha(t){return this._rgb.a=ee(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=ks(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Oi(this._rgb,2,t),this}darken(t){return Oi(this._rgb,2,-t),this}saturate(t){return Oi(this._rgb,1,t),this}desaturate(t){return Oi(this._rgb,1,-t),this}rotate(t){return lh(this._rgb,t),this}};function Nt(){}var Hr=(()=>{let s=0;return()=>s++})();function A(s){return s==null}function B(s){if(Array.isArray&&Array.isArray(s))return!0;let t=Object.prototype.toString.call(s);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function L(s){return s!==null&&Object.prototype.toString.call(s)==="[object Object]"}function Z(s){return(typeof s=="number"||s instanceof Number)&&isFinite(+s)}function ut(s,t){return Z(s)?s:t}function I(s,t){return typeof s>"u"?t:s}var Br=(s,t)=>typeof s=="string"&&s.endsWith("%")?parseFloat(s)/100:+s/t,An=(s,t)=>typeof s=="string"&&s.endsWith("%")?parseFloat(s)/100*t:+s;function H(s,t,e){if(s&&typeof s.call=="function")return s.apply(e,t)}function z(s,t,e,i){let n,o,r;if(B(s))if(o=s.length,i)for(n=o-1;n>=0;n--)t.call(e,s[n],n);else for(n=0;n<o;n++)t.call(e,s[n],n);else if(L(s))for(r=Object.keys(s),o=r.length,n=0;n<o;n++)t.call(e,s[r[n]],r[n])}function Ts(s,t){let e,i,n,o;if(!s||!t||s.length!==t.length)return!1;for(e=0,i=s.length;e<i;++e)if(n=s[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Fi(s){if(B(s))return s.map(Fi);if(L(s)){let t=Object.create(null),e=Object.keys(s),i=e.length,n=0;for(;n<i;++n)t[e[n]]=Fi(s[e[n]]);return t}return s}function $r(s){return["__proto__","prototype","constructor"].indexOf(s)===-1}function yh(s,t,e,i){if(!$r(s))return;let n=t[s],o=e[s];L(n)&&L(o)?ze(n,o,i):t[s]=Fi(o)}function ze(s,t,e){let i=B(t)?t:[t],n=i.length;if(!L(s))return s;e=e||{};let o=e.merger||yh,r;for(let a=0;a<n;++a){if(r=i[a],!L(r))continue;let l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],s,r,e)}return s}function He(s,t){return ze(s,t,{merger:bh})}function bh(s,t,e){if(!$r(s))return;let i=t[s],n=e[s];L(i)&&L(n)?He(i,n):Object.prototype.hasOwnProperty.call(t,s)||(t[s]=Fi(n))}var Cr={"":s=>s,x:s=>s.x,y:s=>s.y};function xh(s){let t=s.split("."),e=[],i="";for(let n of t)i+=n,i.endsWith("\\")?i=i.slice(0,-1)+".":(e.push(i),i="");return e}function _h(s){let t=xh(s);return e=>{for(let i of t){if(i==="")break;e=e&&e[i]}return e}}function Ut(s,t){return(Cr[t]||(Cr[t]=_h(t)))(s)}function Ni(s){return s.charAt(0).toUpperCase()+s.slice(1)}var Be=s=>typeof s<"u",$t=s=>typeof s=="function",Ln=(s,t)=>{if(s.size!==t.size)return!1;for(let e of s)if(!t.has(e))return!1;return!0};function jr(s){return s.type==="mouseup"||s.type==="click"||s.type==="contextmenu"}var $=Math.PI,j=2*$,wh=j+$,Ai=Number.POSITIVE_INFINITY,Sh=$/180,X=$/2,be=$/4,Fr=$*2/3,jt=Math.log10,Ot=Math.sign;function $e(s,t,e){return Math.abs(s-t)<e}function Pn(s){let t=Math.round(s);s=$e(s,t,s/1e3)?t:s;let e=Math.pow(10,Math.floor(jt(s))),i=s/e;return(i<=1?1:i<=2?2:i<=5?5:10)*e}function Ur(s){let t=[],e=Math.sqrt(s),i;for(i=1;i<e;i++)s%i===0&&(t.push(i),t.push(s/i));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function kh(s){return typeof s=="symbol"||typeof s=="object"&&s!==null&&!(Symbol.toPrimitive in s||"toString"in s||"valueOf"in s)}function we(s){return!kh(s)&&!isNaN(parseFloat(s))&&isFinite(s)}function Yr(s,t){let e=Math.round(s);return e-t<=s&&e+t>=s}function Nn(s,t,e){let i,n,o;for(i=0,n=s.length;i<n;i++)o=s[i][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function wt(s){return s*($/180)}function Ri(s){return s*(180/$)}function Rn(s){if(!Z(s))return;let t=1,e=0;for(;Math.round(s*t)/t!==s;)t*=10,e++;return e}function Wn(s,t){let e=t.x-s.x,i=t.y-s.y,n=Math.sqrt(e*e+i*i),o=Math.atan2(i,e);return o<-.5*$&&(o+=j),{angle:o,distance:n}}function Li(s,t){return Math.sqrt(Math.pow(t.x-s.x,2)+Math.pow(t.y-s.y,2))}function Mh(s,t){return(s-t+wh)%j-$}function ht(s){return(s%j+j)%j}function je(s,t,e,i){let n=ht(s),o=ht(t),r=ht(e),a=ht(o-n),l=ht(r-n),c=ht(n-o),h=ht(n-r);return n===o||n===r||i&&o===r||a>l&&c<h}function tt(s,t,e){return Math.max(t,Math.min(e,s))}function Zr(s){return tt(s,-32768,32767)}function Rt(s,t,e,i=1e-6){return s>=Math.min(t,e)-i&&s<=Math.max(t,e)+i}function Wi(s,t,e){e=e||(r=>s[r]<t);let i=s.length-1,n=0,o;for(;i-n>1;)o=n+i>>1,e(o)?n=o:i=o;return{lo:n,hi:i}}var Lt=(s,t,e,i)=>Wi(s,e,i?n=>{let o=s[n][t];return o<e||o===e&&s[n+1][t]===e}:n=>s[n][t]<e),qr=(s,t,e)=>Wi(s,e,i=>s[i][t]>=e);function Gr(s,t,e){let i=0,n=s.length;for(;i<n&&s[i]<t;)i++;for(;n>i&&s[n-1]>e;)n--;return i>0||n<s.length?s.slice(i,n):s}var Xr=["push","pop","shift","splice","unshift"];function Kr(s,t){if(s._chartjs){s._chartjs.listeners.push(t);return}Object.defineProperty(s,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Xr.forEach(e=>{let i="_onData"+Ni(e),n=s[e];Object.defineProperty(s,e,{configurable:!0,enumerable:!1,value(...o){let r=n.apply(this,o);return s._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...o)}),r}})})}function zn(s,t){let e=s._chartjs;if(!e)return;let i=e.listeners,n=i.indexOf(t);n!==-1&&i.splice(n,1),!(i.length>0)&&(Xr.forEach(o=>{delete s[o]}),delete s._chartjs)}function Vn(s){let t=new Set(s);return t.size===s.length?s:Array.from(t)}var Hn=function(){return typeof window>"u"?function(s){return s()}:window.requestAnimationFrame}();function Bn(s,t){let e=[],i=!1;return function(...n){e=n,i||(i=!0,Hn.call(window,()=>{i=!1,s.apply(t,e)}))}}function Jr(s,t){let e;return function(...i){return t?(clearTimeout(e),e=setTimeout(s,t,i)):s.apply(this,i),t}}var zi=s=>s==="start"?"left":s==="end"?"right":"center",ot=(s,t,e)=>s==="start"?t:s==="end"?e:(t+e)/2,Qr=(s,t,e,i)=>s===(i?"left":"right")?e:s==="center"?(t+e)/2:t;function $n(s,t,e){let i=t.length,n=0,o=i;if(s._sorted){let{iScale:r,vScale:a,_parsed:l}=s,c=s.dataset&&s.dataset.options?s.dataset.options.spanGaps:null,h=r.axis,{min:u,max:d,minDefined:f,maxDefined:m}=r.getUserBounds();if(f){if(n=Math.min(Lt(l,h,u).lo,e?i:Lt(t,h,r.getPixelForValue(u)).lo),c){let g=l.slice(0,n+1).reverse().findIndex(p=>!A(p[a.axis]));n-=Math.max(0,g)}n=tt(n,0,i-1)}if(m){let g=Math.max(Lt(l,r.axis,d,!0).hi+1,e?0:Lt(t,h,r.getPixelForValue(d),!0).hi+1);if(c){let p=l.slice(g-1).findIndex(y=>!A(y[a.axis]));g+=Math.max(0,p)}o=tt(g,n,i)-n}else o=i-n}return{start:n,count:o}}function jn(s){let{xScale:t,yScale:e,_scaleRanges:i}=s,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!i)return s._scaleRanges=n,!0;let o=i.xmin!==t.min||i.xmax!==t.max||i.ymin!==e.min||i.ymax!==e.max;return Object.assign(i,n),o}var Di=s=>s===0||s===1,Ar=(s,t,e)=>-(Math.pow(2,10*(s-=1))*Math.sin((s-t)*j/e)),Lr=(s,t,e)=>Math.pow(2,-10*s)*Math.sin((s-t)*j/e)+1,We={linear:s=>s,easeInQuad:s=>s*s,easeOutQuad:s=>-s*(s-2),easeInOutQuad:s=>(s/=.5)<1?.5*s*s:-.5*(--s*(s-2)-1),easeInCubic:s=>s*s*s,easeOutCubic:s=>(s-=1)*s*s+1,easeInOutCubic:s=>(s/=.5)<1?.5*s*s*s:.5*((s-=2)*s*s+2),easeInQuart:s=>s*s*s*s,easeOutQuart:s=>-((s-=1)*s*s*s-1),easeInOutQuart:s=>(s/=.5)<1?.5*s*s*s*s:-.5*((s-=2)*s*s*s-2),easeInQuint:s=>s*s*s*s*s,easeOutQuint:s=>(s-=1)*s*s*s*s+1,easeInOutQuint:s=>(s/=.5)<1?.5*s*s*s*s*s:.5*((s-=2)*s*s*s*s+2),easeInSine:s=>-Math.cos(s*X)+1,easeOutSine:s=>Math.sin(s*X),easeInOutSine:s=>-.5*(Math.cos($*s)-1),easeInExpo:s=>s===0?0:Math.pow(2,10*(s-1)),easeOutExpo:s=>s===1?1:-Math.pow(2,-10*s)+1,easeInOutExpo:s=>Di(s)?s:s<.5?.5*Math.pow(2,10*(s*2-1)):.5*(-Math.pow(2,-10*(s*2-1))+2),easeInCirc:s=>s>=1?s:-(Math.sqrt(1-s*s)-1),easeOutCirc:s=>Math.sqrt(1-(s-=1)*s),easeInOutCirc:s=>(s/=.5)<1?-.5*(Math.sqrt(1-s*s)-1):.5*(Math.sqrt(1-(s-=2)*s)+1),easeInElastic:s=>Di(s)?s:Ar(s,.075,.3),easeOutElastic:s=>Di(s)?s:Lr(s,.075,.3),easeInOutElastic(s){return Di(s)?s:s<.5?.5*Ar(s*2,.1125,.45):.5+.5*Lr(s*2-1,.1125,.45)},easeInBack(s){return s*s*((1.70158+1)*s-1.70158)},easeOutBack(s){return(s-=1)*s*((1.70158+1)*s********)+1},easeInOutBack(s){let t=1.70158;return(s/=.5)<1?.5*(s*s*(((t*=1.525)+1)*s-t)):.5*((s-=2)*s*(((t*=1.525)+1)*s+t)+2)},easeInBounce:s=>1-We.easeOutBounce(1-s),easeOutBounce(s){return s<1/2.75?7.5625*s*s:s<2/2.75?7.5625*(s-=1.5/2.75)*s+.75:s<2.5/2.75?7.5625*(s-=2.25/2.75)*s+.9375:7.5625*(s-=2.625/2.75)*s+.984375},easeInOutBounce:s=>s<.5?We.easeInBounce(s*2)*.5:We.easeOutBounce(s*2-1)*.5+.5};function Un(s){if(s&&typeof s=="object"){let t=s.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Yn(s){return Un(s)?s:new Ss(s)}function In(s){return Un(s)?s:new Ss(s).saturate(.5).darken(.1).hexString()}var vh=["x","y","borderWidth","radius","tension"],Th=["color","borderColor","backgroundColor"];function Oh(s){s.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),s.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),s.set("animations",{colors:{type:"color",properties:Th},numbers:{type:"number",properties:vh}}),s.describe("animations",{_fallback:"animation"}),s.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Dh(s){s.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}var Pr=new Map;function Eh(s,t){t=t||{};let e=s+JSON.stringify(t),i=Pr.get(e);return i||(i=new Intl.NumberFormat(s,t),Pr.set(e,i)),i}function Ue(s,t,e){return Eh(t,e).format(s)}var ta={values(s){return B(s)?s:""+s},numeric(s,t,e){if(s===0)return"0";let i=this.chart.options.locale,n,o=s;if(e.length>1){let c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Ih(s,e)}let r=jt(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Ue(s,i,l)},logarithmic(s,t,e){if(s===0)return"0";let i=e[t].significand||s/Math.pow(10,Math.floor(jt(s)));return[1,2,3,5,10,15].includes(i)||t>.8*e.length?ta.numeric.call(this,s,t,e):""}};function Ih(s,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&s!==Math.floor(s)&&(e=s-Math.floor(s)),e}var Os={formatters:ta};function Ch(s){s.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Os.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),s.route("scale.ticks","color","","color"),s.route("scale.grid","color","","borderColor"),s.route("scale.border","color","","borderColor"),s.route("scale.title","color","","color"),s.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),s.describe("scales",{_fallback:"scale"}),s.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}var ie=Object.create(null),Vi=Object.create(null);function Ms(s,t){if(!t)return s;let e=t.split(".");for(let i=0,n=e.length;i<n;++i){let o=e[i];s=s[o]||(s[o]=Object.create(null))}return s}function Cn(s,t,e){return typeof t=="string"?ze(Ms(s,t),e):ze(Ms(s,""),t)}var Fn=class{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,n)=>In(n.backgroundColor),this.hoverBorderColor=(i,n)=>In(n.borderColor),this.hoverColor=(i,n)=>In(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Cn(this,t,e)}get(t){return Ms(this,t)}describe(t,e){return Cn(Vi,t,e)}override(t,e){return Cn(ie,t,e)}route(t,e,i,n){let o=Ms(this,t),r=Ms(this,i),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){let l=this[a],c=r[n];return L(l)?Object.assign({},c,l):I(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}},U=new Fn({_scriptable:s=>!s.startsWith("on"),_indexable:s=>s!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Oh,Dh,Ch]);function Fh(s){return!s||A(s.size)||A(s.family)?null:(s.style?s.style+" ":"")+(s.weight?s.weight+" ":"")+s.size+"px "+s.family}function vs(s,t,e,i,n){let o=t[n];return o||(o=t[n]=s.measureText(n).width,e.push(n)),o>i&&(i=o),i}function ea(s,t,e,i){i=i||{};let n=i.data=i.data||{},o=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(n=i.data={},o=i.garbageCollect=[],i.font=t),s.save(),s.font=t;let r=0,a=e.length,l,c,h,u,d;for(l=0;l<a;l++)if(u=e[l],u!=null&&!B(u))r=vs(s,n,o,r,u);else if(B(u))for(c=0,h=u.length;c<h;c++)d=u[c],d!=null&&!B(d)&&(r=vs(s,n,o,r,d));s.restore();let f=o.length/2;if(f>e.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return r}function ne(s,t,e){let i=s.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*i)/i+n}function Zn(s,t){!t&&!s||(t=t||s.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,s.width,s.height),t.restore())}function Hi(s,t,e,i){qn(s,t,e,i,null)}function qn(s,t,e,i,n){let o,r,a,l,c,h,u,d,f=t.pointStyle,m=t.rotation,g=t.radius,p=(m||0)*Sh;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){s.save(),s.translate(e,i),s.rotate(p),s.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),s.restore();return}if(!(isNaN(g)||g<=0)){switch(s.beginPath(),f){default:n?s.ellipse(e,i,n/2,g,0,0,j):s.arc(e,i,g,0,j),s.closePath();break;case"triangle":h=n?n/2:g,s.moveTo(e+Math.sin(p)*h,i-Math.cos(p)*g),p+=Fr,s.lineTo(e+Math.sin(p)*h,i-Math.cos(p)*g),p+=Fr,s.lineTo(e+Math.sin(p)*h,i-Math.cos(p)*g),s.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(p+be)*l,u=Math.cos(p+be)*(n?n/2-c:l),a=Math.sin(p+be)*l,d=Math.sin(p+be)*(n?n/2-c:l),s.arc(e-u,i-a,c,p-$,p-X),s.arc(e+d,i-r,c,p-X,p),s.arc(e+u,i+a,c,p,p+X),s.arc(e-d,i+r,c,p+X,p+$),s.closePath();break;case"rect":if(!m){l=Math.SQRT1_2*g,h=n?n/2:l,s.rect(e-h,i-l,2*h,2*l);break}p+=be;case"rectRot":u=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),s.moveTo(e-u,i-a),s.lineTo(e+d,i-r),s.lineTo(e+u,i+a),s.lineTo(e-d,i+r),s.closePath();break;case"crossRot":p+=be;case"cross":u=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),s.moveTo(e-u,i-a),s.lineTo(e+u,i+a),s.moveTo(e+d,i-r),s.lineTo(e-d,i+r);break;case"star":u=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),s.moveTo(e-u,i-a),s.lineTo(e+u,i+a),s.moveTo(e+d,i-r),s.lineTo(e-d,i+r),p+=be,u=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,d=Math.sin(p)*(n?n/2:g),s.moveTo(e-u,i-a),s.lineTo(e+u,i+a),s.moveTo(e+d,i-r),s.lineTo(e-d,i+r);break;case"line":r=n?n/2:Math.cos(p)*g,a=Math.sin(p)*g,s.moveTo(e-r,i-a),s.lineTo(e+r,i+a);break;case"dash":s.moveTo(e,i),s.lineTo(e+Math.cos(p)*(n?n/2:g),i+Math.sin(p)*g);break;case!1:s.closePath();break}s.fill(),t.borderWidth>0&&s.stroke()}}function Pt(s,t,e){return e=e||.5,!t||s&&s.x>t.left-e&&s.x<t.right+e&&s.y>t.top-e&&s.y<t.bottom+e}function Ds(s,t){s.save(),s.beginPath(),s.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),s.clip()}function Es(s){s.restore()}function sa(s,t,e,i,n){if(!t)return s.lineTo(e.x,e.y);if(n==="middle"){let o=(t.x+e.x)/2;s.lineTo(o,t.y),s.lineTo(o,e.y)}else n==="after"!=!!i?s.lineTo(t.x,e.y):s.lineTo(e.x,t.y);s.lineTo(e.x,e.y)}function ia(s,t,e,i){if(!t)return s.lineTo(e.x,e.y);s.bezierCurveTo(i?t.cp1x:t.cp2x,i?t.cp1y:t.cp2y,i?e.cp2x:e.cp1x,i?e.cp2y:e.cp1y,e.x,e.y)}function Ah(s,t){t.translation&&s.translate(t.translation[0],t.translation[1]),A(t.rotation)||s.rotate(t.rotation),t.color&&(s.fillStyle=t.color),t.textAlign&&(s.textAlign=t.textAlign),t.textBaseline&&(s.textBaseline=t.textBaseline)}function Lh(s,t,e,i,n){if(n.strikethrough||n.underline){let o=s.measureText(i),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;s.strokeStyle=s.fillStyle,s.beginPath(),s.lineWidth=n.decorationWidth||2,s.moveTo(r,h),s.lineTo(a,h),s.stroke()}}function Ph(s,t){let e=s.fillStyle;s.fillStyle=t.color,s.fillRect(t.left,t.top,t.width,t.height),s.fillStyle=e}function oe(s,t,e,i,n,o={}){let r=B(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="",l,c;for(s.save(),s.font=n.string,Ah(s,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Ph(s,o.backdrop),a&&(o.strokeColor&&(s.strokeStyle=o.strokeColor),A(o.strokeWidth)||(s.lineWidth=o.strokeWidth),s.strokeText(c,e,i,o.maxWidth)),s.fillText(c,e,i,o.maxWidth),Lh(s,e,i,c,o),i+=Number(n.lineHeight);s.restore()}function Ye(s,t){let{x:e,y:i,w:n,h:o,radius:r}=t;s.arc(e+r.topLeft,i+r.topLeft,r.topLeft,1.5*$,$,!0),s.lineTo(e,i+o-r.bottomLeft),s.arc(e+r.bottomLeft,i+o-r.bottomLeft,r.bottomLeft,$,X,!0),s.lineTo(e+n-r.bottomRight,i+o),s.arc(e+n-r.bottomRight,i+o-r.bottomRight,r.bottomRight,X,0,!0),s.lineTo(e+n,i+r.topRight),s.arc(e+n-r.topRight,i+r.topRight,r.topRight,0,-X,!0),s.lineTo(e+r.topLeft,i)}var Nh=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Rh=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Wh(s,t){let e=(""+s).match(Nh);if(!e||e[1]==="normal")return t*1.2;switch(s=+e[2],e[3]){case"px":return s;case"%":s/=100;break}return t*s}var zh=s=>+s||0;function Bi(s,t){let e={},i=L(t),n=i?Object.keys(t):t,o=L(s)?i?r=>I(s[r],s[t[r]]):r=>s[r]:()=>s;for(let r of n)e[r]=zh(o(r));return e}function Gn(s){return Bi(s,{top:"y",right:"x",bottom:"y",left:"x"})}function re(s){return Bi(s,["topLeft","topRight","bottomLeft","bottomRight"])}function rt(s){let t=Gn(s);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Q(s,t){s=s||{},t=t||U.font;let e=I(s.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let i=I(s.style,t.style);i&&!(""+i).match(Rh)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);let n={family:I(s.family,t.family),lineHeight:Wh(I(s.lineHeight,t.lineHeight),e),size:e,style:i,weight:I(s.weight,t.weight),string:""};return n.string=Fh(n),n}function Ze(s,t,e,i){let n=!0,o,r,a;for(o=0,r=s.length;o<r;++o)if(a=s[o],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),n=!1),e!==void 0&&B(a)&&(a=a[e%a.length],n=!1),a!==void 0))return i&&!n&&(i.cacheable=!1),a}function na(s,t,e){let{min:i,max:n}=s,o=An(t,(n-i)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(i,-Math.abs(o)),max:r(n,o)}}function Yt(s,t){return Object.assign(Object.create(s),t)}function $i(s,t=[""],e,i,n=()=>s[0]){let o=e||s;typeof i>"u"&&(i=aa("_fallback",s));let r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:s,_rootScopes:o,_fallback:i,_getTarget:n,override:a=>$i([a,...s],t,o,i)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete s[0][l],!0},get(a,l){return oa(a,l,()=>Zh(l,t,s,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(s[0])},has(a,l){return Rr(a).includes(l)},ownKeys(a){return Rr(a)},set(a,l,c){let h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function _e(s,t,e,i){let n={_cacheable:!1,_proxy:s,_context:t,_subProxy:e,_stack:new Set,_descriptors:Xn(s,i),setContext:o=>_e(s,o,e,i),override:o=>_e(s.override(o),t,e,i)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete s[r],!0},get(o,r,a){return oa(o,r,()=>Hh(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(s,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(s,r)},getPrototypeOf(){return Reflect.getPrototypeOf(s)},has(o,r){return Reflect.has(s,r)},ownKeys(){return Reflect.ownKeys(s)},set(o,r,a){return s[r]=a,delete o[r],!0}})}function Xn(s,t={scriptable:!0,indexable:!0}){let{_scriptable:e=t.scriptable,_indexable:i=t.indexable,_allKeys:n=t.allKeys}=s;return{allKeys:n,scriptable:e,indexable:i,isScriptable:$t(e)?e:()=>e,isIndexable:$t(i)?i:()=>i}}var Vh=(s,t)=>s?s+Ni(t):t,Kn=(s,t)=>L(t)&&s!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function oa(s,t,e){if(Object.prototype.hasOwnProperty.call(s,t)||t==="constructor")return s[t];let i=e();return s[t]=i,i}function Hh(s,t,e){let{_proxy:i,_context:n,_subProxy:o,_descriptors:r}=s,a=i[t];return $t(a)&&r.isScriptable(t)&&(a=Bh(t,a,s,e)),B(a)&&a.length&&(a=$h(t,a,s,r.isIndexable)),Kn(t,a)&&(a=_e(a,n,o&&o[t],r)),a}function Bh(s,t,e,i){let{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(s))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+s);a.add(s);let l=t(o,r||i);return a.delete(s),Kn(s,l)&&(l=Jn(n._scopes,n,s,l)),l}function $h(s,t,e,i){let{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&i(s))return t[o.index%t.length];if(L(t[0])){let l=t,c=n._scopes.filter(h=>h!==l);t=[];for(let h of l){let u=Jn(c,n,s,h);t.push(_e(u,o,r&&r[s],a))}}return t}function ra(s,t,e){return $t(s)?s(t,e):s}var jh=(s,t)=>s===!0?t:typeof s=="string"?Ut(t,s):void 0;function Uh(s,t,e,i,n){for(let o of t){let r=jh(e,o);if(r){s.add(r);let a=ra(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==i)return a}else if(r===!1&&typeof i<"u"&&e!==i)return null}return!1}function Jn(s,t,e,i){let n=t._rootScopes,o=ra(t._fallback,e,i),r=[...s,...n],a=new Set;a.add(i);let l=Nr(a,r,e,o||e,i);return l===null||typeof o<"u"&&o!==e&&(l=Nr(a,r,o,l,i),l===null)?!1:$i(Array.from(a),[""],n,o,()=>Yh(t,e,i))}function Nr(s,t,e,i,n){for(;e;)e=Uh(s,t,e,i,n);return e}function Yh(s,t,e){let i=s._getTarget();t in i||(i[t]={});let n=i[t];return B(n)&&L(e)?e:n||{}}function Zh(s,t,e,i){let n;for(let o of t)if(n=aa(Vh(o,s),e),typeof n<"u")return Kn(s,n)?Jn(e,i,s,n):n}function aa(s,t){for(let e of t){if(!e)continue;let i=e[s];if(typeof i<"u")return i}}function Rr(s){let t=s._keys;return t||(t=s._keys=qh(s._scopes)),t}function qh(s){let t=new Set;for(let e of s)for(let i of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(i);return Array.from(t)}function Qn(s,t,e,i){let{iScale:n}=s,{key:o="r"}=this._parsing,r=new Array(i),a,l,c,h;for(a=0,l=i;a<l;++a)c=a+e,h=t[c],r[a]={r:n.parse(Ut(h,o),c)};return r}var Gh=Number.EPSILON||1e-14,Ve=(s,t)=>t<s.length&&!s[t].skip&&s[t],la=s=>s==="x"?"y":"x";function Xh(s,t,e,i){let n=s.skip?t:s,o=t,r=e.skip?t:e,a=Li(o,n),l=Li(r,o),c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;let u=i*c,d=i*h;return{previous:{x:o.x-u*(r.x-n.x),y:o.y-u*(r.y-n.y)},next:{x:o.x+d*(r.x-n.x),y:o.y+d*(r.y-n.y)}}}function Kh(s,t,e){let i=s.length,n,o,r,a,l,c=Ve(s,0);for(let h=0;h<i-1;++h)if(l=c,c=Ve(s,h+1),!(!l||!c)){if($e(t[h],0,Gh)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function Jh(s,t,e="x"){let i=la(e),n=s.length,o,r,a,l=Ve(s,0);for(let c=0;c<n;++c){if(r=a,a=l,l=Ve(s,c+1),!a)continue;let h=a[e],u=a[i];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${i}`]=u-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${i}`]=u+o*t[c])}}function Qh(s,t="x"){let e=la(t),i=s.length,n=Array(i).fill(0),o=Array(i),r,a,l,c=Ve(s,0);for(r=0;r<i;++r)if(a=l,l=c,c=Ve(s,r+1),!!l){if(c){let h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?Ot(n[r-1])!==Ot(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}Kh(s,n,o),Jh(s,o,t)}function Ei(s,t,e){return Math.max(Math.min(s,e),t)}function tu(s,t){let e,i,n,o,r,a=Pt(s[0],t);for(e=0,i=s.length;e<i;++e)r=o,o=a,a=e<i-1&&Pt(s[e+1],t),o&&(n=s[e],r&&(n.cp1x=Ei(n.cp1x,t.left,t.right),n.cp1y=Ei(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=Ei(n.cp2x,t.left,t.right),n.cp2y=Ei(n.cp2y,t.top,t.bottom)))}function ca(s,t,e,i,n){let o,r,a,l;if(t.spanGaps&&(s=s.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")Qh(s,n);else{let c=i?s[s.length-1]:s[0];for(o=0,r=s.length;o<r;++o)a=s[o],l=Xh(c,a,s[Math.min(o+1,r-(i?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&tu(s,e)}function ji(){return typeof window<"u"&&typeof document<"u"}function Ui(s){let t=s.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Pi(s,t,e){let i;return typeof s=="string"?(i=parseInt(s,10),s.indexOf("%")!==-1&&(i=i/100*t.parentNode[e])):i=s,i}var Yi=s=>s.ownerDocument.defaultView.getComputedStyle(s,null);function eu(s,t){return Yi(s).getPropertyValue(t)}var su=["top","right","bottom","left"];function xe(s,t,e){let i={};e=e?"-"+e:"";for(let n=0;n<4;n++){let o=su[n];i[o]=parseFloat(s[t+"-"+o+e])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}var iu=(s,t,e)=>(s>0||t>0)&&(!e||!e.shadowRoot);function nu(s,t){let e=s.touches,i=e&&e.length?e[0]:s,{offsetX:n,offsetY:o}=i,r=!1,a,l;if(iu(n,o,s.target))a=n,l=o;else{let c=t.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function ae(s,t){if("native"in s)return s;let{canvas:e,currentDevicePixelRatio:i}=t,n=Yi(e),o=n.boxSizing==="border-box",r=xe(n,"padding"),a=xe(n,"border","width"),{x:l,y:c,box:h}=nu(s,e),u=r.left+(h&&a.left),d=r.top+(h&&a.top),{width:f,height:m}=t;return o&&(f-=r.width+a.width,m-=r.height+a.height),{x:Math.round((l-u)/f*e.width/i),y:Math.round((c-d)/m*e.height/i)}}function ou(s,t,e){let i,n;if(t===void 0||e===void 0){let o=s&&Ui(s);if(!o)t=s.clientWidth,e=s.clientHeight;else{let r=o.getBoundingClientRect(),a=Yi(o),l=xe(a,"border","width"),c=xe(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,i=Pi(a.maxWidth,o,"clientWidth"),n=Pi(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:i||Ai,maxHeight:n||Ai}}var Ii=s=>Math.round(s*10)/10;function ha(s,t,e,i){let n=Yi(s),o=xe(n,"margin"),r=Pi(n.maxWidth,s,"clientWidth")||Ai,a=Pi(n.maxHeight,s,"clientHeight")||Ai,l=ou(s,t,e),{width:c,height:h}=l;if(n.boxSizing==="content-box"){let d=xe(n,"border","width"),f=xe(n,"padding");c-=f.width+d.width,h-=f.height+d.height}return c=Math.max(0,c-o.width),h=Math.max(0,i?c/i:h-o.height),c=Ii(Math.min(c,r,l.maxWidth)),h=Ii(Math.min(h,a,l.maxHeight)),c&&!h&&(h=Ii(c/2)),(t!==void 0||e!==void 0)&&i&&l.height&&h>l.height&&(h=l.height,c=Ii(Math.floor(h*i))),{width:c,height:h}}function to(s,t,e){let i=t||1,n=Math.floor(s.height*i),o=Math.floor(s.width*i);s.height=Math.floor(s.height),s.width=Math.floor(s.width);let r=s.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${s.height}px`,r.style.width=`${s.width}px`),s.currentDevicePixelRatio!==i||r.height!==n||r.width!==o?(s.currentDevicePixelRatio=i,r.height=n,r.width=o,s.ctx.setTransform(i,0,0,i,0,0),!0):!1}var ua=function(){let s=!1;try{let t={get passive(){return s=!0,!1}};ji()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return s}();function eo(s,t){let e=eu(s,t),i=e&&e.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function se(s,t,e,i){return{x:s.x+e*(t.x-s.x),y:s.y+e*(t.y-s.y)}}function da(s,t,e,i){return{x:s.x+e*(t.x-s.x),y:i==="middle"?e<.5?s.y:t.y:i==="after"?e<1?s.y:t.y:e>0?t.y:s.y}}function fa(s,t,e,i){let n={x:s.cp2x,y:s.cp2y},o={x:t.cp1x,y:t.cp1y},r=se(s,n,e),a=se(n,o,e),l=se(o,t,e),c=se(r,a,e),h=se(a,l,e);return se(c,h,e)}var ru=function(s,t){return{x(e){return s+s+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,i){return e-i},leftForLtr(e,i){return e-i}}},au=function(){return{x(s){return s},setWidth(s){},textAlign(s){return s},xPlus(s,t){return s+t},leftForLtr(s,t){return s}}};function Se(s,t,e){return s?ru(t,e):au()}function so(s,t){let e,i;(t==="ltr"||t==="rtl")&&(e=s.canvas.style,i=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),s.prevTextDirection=i)}function io(s,t){t!==void 0&&(delete s.prevTextDirection,s.canvas.style.setProperty("direction",t[0],t[1]))}function ma(s){return s==="angle"?{between:je,compare:Mh,normalize:ht}:{between:Rt,compare:(t,e)=>t-e,normalize:t=>t}}function Wr({start:s,end:t,count:e,loop:i,style:n}){return{start:s%e,end:t%e,loop:i&&(t-s+1)%e===0,style:n}}function lu(s,t,e){let{property:i,start:n,end:o}=e,{between:r,normalize:a}=ma(i),l=t.length,{start:c,end:h,loop:u}=s,d,f;if(u){for(c+=l,h+=l,d=0,f=l;d<f&&r(a(t[c%l][i]),n,o);++d)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:u,style:s.style}}function no(s,t,e){if(!e)return[s];let{property:i,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=ma(i),{start:h,end:u,loop:d,style:f}=lu(s,t,e),m=[],g=!1,p=null,y,b,_,w=()=>l(n,_,y)&&a(n,_)!==0,x=()=>a(o,y)===0||l(o,_,y),k=()=>g||w(),M=()=>!g||x();for(let v=h,O=h;v<=u;++v)b=t[v%r],!b.skip&&(y=c(b[i]),y!==_&&(g=l(y,n,o),p===null&&k()&&(p=a(y,n)===0?v:O),p!==null&&M()&&(m.push(Wr({start:p,end:v,loop:d,count:r,style:f})),p=null),O=v,_=y));return p!==null&&m.push(Wr({start:p,end:u,loop:d,count:r,style:f})),m}function oo(s,t){let e=[],i=s.segments;for(let n=0;n<i.length;n++){let o=no(i[n],s.points,t);o.length&&e.push(...o)}return e}function cu(s,t,e,i){let n=0,o=t-1;if(e&&!i)for(;n<t&&!s[n].skip;)n++;for(;n<t&&s[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&s[o%t].skip;)o--;return o%=t,{start:n,end:o}}function hu(s,t,e,i){let n=s.length,o=[],r=t,a=s[t],l;for(l=t+1;l<=e;++l){let c=s[l%n];c.skip||c.stop?a.skip||(i=!1,o.push({start:t%n,end:(l-1)%n,loop:i}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:i}),o}function ga(s,t){let e=s.points,i=s.options.spanGaps,n=e.length;if(!n)return[];let o=!!s._loop,{start:r,end:a}=cu(e,n,o,i);if(i===!0)return zr(s,[{start:r,end:a,loop:o}],e,t);let l=a<r?a+n:a,c=!!s._fullLoop&&r===0&&a===n-1;return zr(s,hu(e,r,l,c),e,t)}function zr(s,t,e,i){return!i||!i.setContext||!e?t:uu(s,t,e,i)}function uu(s,t,e,i){let n=s._chart.getContext(),o=Vr(s.options),{_datasetIndex:r,options:{spanGaps:a}}=s,l=e.length,c=[],h=o,u=t[0].start,d=u;function f(m,g,p,y){let b=a?-1:1;if(m!==g){for(m+=l;e[m%l].skip;)m-=b;for(;e[g%l].skip;)g+=b;m%l!==g%l&&(c.push({start:m%l,end:g%l,loop:p,style:y}),h=y,u=g%l)}}for(let m of t){u=a?u:m.start;let g=e[u%l],p;for(d=u+1;d<=m.end;d++){let y=e[d%l];p=Vr(i.setContext(Yt(n,{type:"segment",p0:g,p1:y,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:r}))),du(p,h)&&f(u,d-1,m.loop,h),g=y,h=p}u<d-1&&f(u,d-1,m.loop,h)}return c}function Vr(s){return{backgroundColor:s.backgroundColor,borderCapStyle:s.borderCapStyle,borderDash:s.borderDash,borderDashOffset:s.borderDashOffset,borderJoinStyle:s.borderJoinStyle,borderWidth:s.borderWidth,borderColor:s.borderColor}}function du(s,t){if(!t)return!1;let e=[],i=function(n,o){return Un(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(s,i)!==JSON.stringify(t,i)}function Ci(s,t,e){return s.options.clip?s[e]:t[e]}function fu(s,t){let{xScale:e,yScale:i}=s;return e&&i?{left:Ci(e,t,"left"),right:Ci(e,t,"right"),top:Ci(i,t,"top"),bottom:Ci(i,t,"bottom")}:t}function ro(s,t){let e=t._clip;if(e.disabled)return!1;let i=fu(t,s.chartArea);return{left:e.left===!1?0:i.left-(e.left===!0?0:e.left),right:e.right===!1?s.width:i.right+(e.right===!0?0:e.right),top:e.top===!1?0:i.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?s.height:i.bottom+(e.bottom===!0?0:e.bottom)}}var bo=class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,n){let o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(i-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Hn.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,n)=>{if(!i.running||!i.items.length)return;let o=i.items,r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,i,t,"progress")),o.length||(i.running=!1,this._notify(n,i,t,"complete"),i.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((i,n)=>Math.max(i,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,n=i.length-1;for(;n>=0;--n)i[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}},Zt=new bo,pa="transparent",mu={boolean(s,t,e){return e>.5?t:s},color(s,t,e){let i=Yn(s||pa),n=i.valid&&Yn(t||pa);return n&&n.valid?n.mix(i,e).hexString():t},number(s,t,e){return s+(t-s)*e}},xo=class{constructor(t,e,i,n){let o=e[i];n=Ze([t.to,n,o,t.from]);let r=Ze([t.from,o,n]);this._active=!0,this._fn=t.fn||mu[t.type||typeof r],this._easing=We[t.easing]||We.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let n=this._target[this._prop],o=i-this._start,r=this._duration-o;this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=Ze([t.to,e,n,t.from]),this._from=Ze([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e=t-this._start,i=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to,l;if(this._active=o!==a&&(r||e<i),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/i%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let n=0;n<i.length;n++)i[n][e]()}},en=class{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!L(t))return;let e=Object.keys(U.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{let o=t[n];if(!L(o))return;let r={};for(let a of e)r[a]=o[a];(B(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!i.has(a))&&i.set(a,r)})})}_animateOptions(t,e){let i=e.options,n=pu(t,i);if(!n)return[];let o=this._createAnimations(n,i);return i.$shared&&gu(t.options.$animations,i).then(()=>{t.options=i},()=>{}),o}_createAnimations(t,e){let i=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now(),l;for(l=r.length-1;l>=0;--l){let c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}let h=e[c],u=o[c],d=i.get(c);if(u)if(d&&u.active()){u.update(d,h,a);continue}else u.cancel();if(!d||!d.duration){t[c]=h;continue}o[c]=u=new xo(d,t,c,h),n.push(u)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}let i=this._createAnimations(t,e);if(i.length)return Zt.add(this._chart,i),!0}};function gu(s,t){let e=[],i=Object.keys(t);for(let n=0;n<i.length;n++){let o=s[i[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function pu(s,t){if(!t)return;let e=s.options;if(!e){s.options=t;return}return e.$shared&&(s.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function ya(s,t){let e=s&&s.options||{},i=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:i?o:n,end:i?n:o}}function yu(s,t,e){if(e===!1)return!1;let i=ya(s,e),n=ya(t,e);return{top:n.end,right:i.end,bottom:n.start,left:i.start}}function bu(s){let t,e,i,n;return L(s)?(t=s.top,e=s.right,i=s.bottom,n=s.left):t=e=i=n=s,{top:t,right:e,bottom:i,left:n,disabled:s===!1}}function gl(s,t){let e=[],i=s._getSortedDatasetMetas(t),n,o;for(n=0,o=i.length;n<o;++n)e.push(i[n].index);return e}function ba(s,t,e,i={}){let n=s.keys,o=i.mode==="single",r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,i.all)continue;break}c=s.values[l],Z(c)&&(o||t===0||Ot(t)===Ot(c))&&(t+=c)}return!h&&!i.all?0:t}function xu(s,t){let{iScale:e,vScale:i}=t,n=e.axis==="x"?"x":"y",o=i.axis==="x"?"x":"y",r=Object.keys(s),a=new Array(r.length),l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:s[h]};return a}function ao(s,t){let e=s&&s.options.stacked;return e||e===void 0&&t.stack!==void 0}function _u(s,t,e){return`${s.id}.${t.id}.${e.stack||e.type}`}function wu(s){let{min:t,max:e,minDefined:i,maxDefined:n}=s.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Su(s,t,e){let i=s[t]||(s[t]={});return i[e]||(i[e]={})}function xa(s,t,e,i){for(let n of t.getMatchingVisibleMetas(i).reverse()){let o=s[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function _a(s,t){let{chart:e,_cachedMeta:i}=s,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=i,l=o.axis,c=r.axis,h=_u(o,r,i),u=t.length,d;for(let f=0;f<u;++f){let m=t[f],{[l]:g,[c]:p}=m,y=m._stacks||(m._stacks={});d=y[c]=Su(n,h,g),d[a]=p,d._top=xa(d,r,!0,i.type),d._bottom=xa(d,r,!1,i.type);let b=d._visualValues||(d._visualValues={});b[a]=p}}function lo(s,t){let e=s.scales;return Object.keys(e).filter(i=>e[i].axis===t).shift()}function ku(s,t){return Yt(s,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Mu(s,t,e){return Yt(s,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Is(s,t){let e=s.controller.index,i=s.vScale&&s.vScale.axis;if(i){t=t||s._parsed;for(let n of t){let o=n._stacks;if(!o||o[i]===void 0||o[i][e]===void 0)return;delete o[i][e],o[i]._visualValues!==void 0&&o[i]._visualValues[e]!==void 0&&delete o[i]._visualValues[e]}}}var co=s=>s==="reset"||s==="none",wa=(s,t)=>t?s:Object.assign({},s),vu=(s,t,e)=>s&&!t.hidden&&t._stacked&&{keys:gl(e,!0),values:null},pt=class{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=ao(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Is(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),n=(u,d,f,m)=>u==="x"?d:u==="r"?m:f,o=e.xAxisID=I(i.xAxisID,lo(t,"x")),r=e.yAxisID=I(i.yAxisID,lo(t,"y")),a=e.rAxisID=I(i.rAxisID,lo(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&zn(this._data,this),t._stacked&&Is(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(L(e)){let n=this._cachedMeta;this._data=xu(e,n)}else if(i!==e){if(i){zn(i,this);let n=this._cachedMeta;Is(n),n._parsed=[]}e&&Object.isExtensible(e)&&Kr(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),n=!1;this._dataCheck();let o=e._stacked;e._stacked=ao(e.vScale,e),e.stack!==i.stack&&(n=!0,Is(e),e.stack=i.stack),this._resyncElements(t),(n||o!==e._stacked)&&(_a(this,e._parsed),e._stacked=ao(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let{_cachedMeta:i,_data:n}=this,{iScale:o,_stacked:r}=i,a=o.axis,l=t===0&&e===n.length?!0:i._sorted,c=t>0&&i._parsed[t-1],h,u,d;if(this._parsing===!1)i._parsed=n,i._sorted=!0,d=n;else{B(n[t])?d=this.parseArrayData(i,n,t,e):L(n[t])?d=this.parseObjectData(i,n,t,e):d=this.parsePrimitiveData(i,n,t,e);let f=()=>u[a]===null||c&&u[a]<c[a];for(h=0;h<e;++h)i._parsed[h+t]=u=d[h],l&&(f()&&(l=!1),c=u);i._sorted=l}r&&_a(this,d)}parsePrimitiveData(t,e,i,n){let{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,u=new Array(n),d,f,m;for(d=0,f=n;d<f;++d)m=d+i,u[d]={[a]:h||o.parse(c[m],m),[l]:r.parse(e[m],m)};return u}parseArrayData(t,e,i,n){let{xScale:o,yScale:r}=t,a=new Array(n),l,c,h,u;for(l=0,c=n;l<c;++l)h=l+i,u=e[h],a[l]={x:o.parse(u[0],h),y:r.parse(u[1],h)};return a}parseObjectData(t,e,i,n){let{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n),h,u,d,f;for(h=0,u=n;h<u;++h)d=h+i,f=e[d],c[h]={x:o.parse(Ut(f,a),d),y:r.parse(Ut(f,l),d)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:gl(n,!0),values:e._stacks[t.axis]._visualValues};return ba(a,r,o.index,{mode:i})}updateRangeFromParsed(t,e,i,n){let o=i[e.axis],r=o===null?NaN:o,a=n&&i._stacks[e.axis];n&&a&&(n.values=a,r=ba(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){let i=this._cachedMeta,n=i._parsed,o=i._sorted&&t===i.iScale,r=n.length,a=this._getOtherScale(t),l=vu(e,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:u}=wu(a),d,f;function m(){f=n[d];let g=f[a.axis];return!Z(f[t.axis])||h>g||u<g}for(d=0;d<r&&!(!m()&&(this.updateRangeFromParsed(c,t,f,l),o));++d);if(o){for(d=r-1;d>=0;--d)if(!m()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){let e=this._cachedMeta._parsed,i=[],n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],Z(r)&&i.push(r);return i}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:i?""+i.getLabelForValue(o[i.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){let e=this._cachedMeta;this.update(t||"default"),e._clip=bu(I(this.options.clip,yu(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){let t=this._ctx,e=this.chart,i=this._cachedMeta,n=i.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop,h;for(i.dataset&&i.dataset.draw(t,o,a,l),h=a;h<a+l;++h){let u=n[h];u.hidden||(u.active&&c?r.push(u):u.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){let i=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){let n=this.getDataset(),o;if(t>=0&&t<this._cachedMeta.data.length){let r=this._cachedMeta.data[t];o=r.$context||(r.$context=Mu(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=ku(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=i,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Be(i);if(a)return wa(a,l);let c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),u=n?[`${t}Hover`,"hover",t,""]:[t,""],d=c.getOptionScopes(this.getDataset(),h),f=Object.keys(U.elements[t]),m=()=>this.getContext(i,n,e),g=c.resolveNamedOptions(d,f,m,u);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(wa(g,l))),g}_resolveAnimations(t,e,i){let n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){let h=this.chart.config,u=h.datasetAnimationScopeKeys(this._type,e),d=h.getOptionScopes(this.getDataset(),u);l=h.createResolver(d,this.getContext(t,i,e))}let c=new en(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||co(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(i),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,i),{sharedOptions:o,includeOptions:r}}updateElement(t,e,i,n){co(n)?Object.assign(t,i):this._resolveAnimations(e,n).update(t,i)}updateSharedOptions(t,e,i){t&&!co(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,n){t.active=n;let o=this.getStyle(e,n);this._resolveAnimations(e,i,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];let n=i.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,i=!0){let n=this._cachedMeta,o=n.data,r=t+e,a,l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),i&&this.updateElements(o,t,e,"reset")}updateElements(t,e,i,n){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let n=i._parsed.splice(t,e);i._stacked&&Is(i,n)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,n]=t;this[e](i,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}};S(pt,"defaults",{}),S(pt,"datasetElementType",null),S(pt,"dataElementType",null);function Tu(s,t){if(!s._cache.$bar){let e=s.getMatchingVisibleMetas(t),i=[];for(let n=0,o=e.length;n<o;n++)i=i.concat(e[n].controller.getAllParsedValues(s));s._cache.$bar=Vn(i.sort((n,o)=>n-o))}return s._cache.$bar}function Ou(s){let t=s.iScale,e=Tu(t,s.type),i=t._length,n,o,r,a,l=()=>{r===32767||r===-32768||(Be(a)&&(i=Math.min(i,Math.abs(r-a)||i)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return i}function Du(s,t,e,i){let n=e.barThickness,o,r;return A(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*i,r=1),{chunk:o/i,ratio:r,start:t.pixels[s]-o/2}}function Eu(s,t,e,i){let n=t.pixels,o=n[s],r=s>0?n[s-1]:null,a=s<n.length-1?n[s+1]:null,l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);let c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/i,ratio:e.barPercentage,start:c}}function Iu(s,t,e,i){let n=e.parse(s[0],i),o=e.parse(s[1],i),r=Math.min(n,o),a=Math.max(n,o),l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function pl(s,t,e,i){return B(s)?Iu(s,t,e,i):t[e.axis]=e.parse(s,i),t}function Sa(s,t,e,i){let n=s.iScale,o=s.vScale,r=n.getLabels(),a=n===o,l=[],c,h,u,d;for(c=e,h=e+i;c<h;++c)d=t[c],u={},u[n.axis]=a||n.parse(r[c],c),l.push(pl(d,u,o,c));return l}function ho(s){return s&&s.barStart!==void 0&&s.barEnd!==void 0}function Cu(s,t,e){return s!==0?Ot(s):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function Fu(s){let t,e,i,n,o;return s.horizontal?(t=s.base>s.x,e="left",i="right"):(t=s.base<s.y,e="bottom",i="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:i,reverse:t,top:n,bottom:o}}function Au(s,t,e,i){let n=t.borderSkipped,o={};if(!n){s.borderSkipped=o;return}if(n===!0){s.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:r,end:a,reverse:l,top:c,bottom:h}=Fu(s);n==="middle"&&e&&(s.enableBorderRadius=!0,(e._top||0)===i?n=c:(e._bottom||0)===i?n=h:(o[ka(h,r,a,l)]=!0,n=c)),o[ka(n,r,a,l)]=!0,s.borderSkipped=o}function ka(s,t,e,i){return i?(s=Lu(s,t,e),s=Ma(s,e,t)):s=Ma(s,t,e),s}function Lu(s,t,e){return s===t?e:s===e?t:s}function Ma(s,t,e){return s==="start"?t:s==="end"?e:s}function Pu(s,{inflateAmount:t},e){s.inflateAmount=t==="auto"?e===1?.33:0:t}var Ge=class extends pt{parsePrimitiveData(t,e,i,n){return Sa(t,e,i,n)}parseArrayData(t,e,i,n){return Sa(t,e,i,n)}parseObjectData(t,e,i,n){let{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,u=[],d,f,m,g;for(d=i,f=i+n;d<f;++d)g=e[d],m={},m[o.axis]=o.parse(Ut(g,c),d),u.push(pl(Ut(g,h),m,r,d));return u}updateRangeFromParsed(t,e,i,n){super.updateRangeFromParsed(t,e,i,n);let o=i._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let e=this._cachedMeta,{iScale:i,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=ho(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+i.getLabelForValue(o[i.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();let t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,n){let o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:u,includeOptions:d}=this._getSharedOptions(e,n);for(let f=e;f<e+i;f++){let m=this.getParsed(f),g=o||A(m[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),p=this._calculateBarIndexPixels(f,h),y=(m._stacks||{})[a.axis],b={horizontal:c,base:g.base,enableBorderRadius:!y||ho(m._custom)||r===y._top||r===y._bottom,x:c?g.head:p.center,y:c?p.center:g.head,height:c?p.size:Math.abs(g.size),width:c?Math.abs(g.size):p.size};d&&(b.options=u||this.resolveDataElementOptions(f,t[f].active?"active":n));let _=b.options||t[f].options;Au(b,_,y,r),Pu(b,_,h.ratio),this.updateElement(t[f],f,b,n)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,n=i.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=i.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[i.axis],c=h=>{let u=h._parsed.find(f=>f[i.axis]===l),d=u&&u[h.vScale.axis];if(A(d)||isNaN(d))return!0};for(let h of n)if(!(e!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let n=this._getStacks(t,i),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){let t=this.options,e=this._cachedMeta,i=e.iScale,n=[],o,r;for(o=0,r=e.data.length;o<r;++o)n.push(i.getPixelForValue(this.getParsed(o)[i.axis],o));let a=t.barThickness;return{min:a||Ou(e),pixels:n,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){let{_cachedMeta:{vScale:e,_stacked:i,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=ho(c),u=l[e.axis],d=0,f=i?this.applyStack(e,l,i):u,m,g;f!==u&&(d=f-u,f=u),h&&(u=c.barStart,f=c.barEnd-c.barStart,u!==0&&Ot(u)!==Ot(c.barEnd)&&(d=0),d+=u);let p=!A(o)&&!h?o:d,y=e.getPixelForValue(p);if(this.chart.getDataVisibility(t)?m=e.getPixelForValue(d+f):m=y,g=m-y,Math.abs(g)<r){g=Cu(g,e,a)*r,u===a&&(y-=g/2);let b=e.getPixelForDecimal(0),_=e.getPixelForDecimal(1),w=Math.min(b,_),x=Math.max(b,_);y=Math.max(Math.min(y,x),w),m=y+g,i&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(m)-e.getValueForPixel(y))}if(y===e.getPixelForValue(a)){let b=Ot(g)*e.getLineWidthForValue(a)/2;y+=b,g-=b}return{size:g,base:y,head:m,center:m+g/2}}_calculateBarIndexPixels(t,e){let i=e.scale,n=this.options,o=n.skipNull,r=I(n.maxBarThickness,1/0),a,l;if(e.grouped){let c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?Eu(t,e,n,c):Du(t,e,n,c),u=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*u+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=i.getPixelForValue(this.getParsed(t)[i.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,n=i.length,o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!i[o].hidden&&i[o].draw(this._ctx)}};S(Ge,"id","bar"),S(Ge,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),S(Ge,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});var Xe=class extends pt{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,n){let o=super.parsePrimitiveData(t,e,i,n);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+i).radius;return o}parseArrayData(t,e,i,n){let o=super.parseArrayData(t,e,i,n);for(let r=0;r<o.length;r++){let a=e[i+r];o[r]._custom=I(a[2],this.resolveDataElementOptions(r+i).radius)}return o}parseObjectData(t,e,i,n){let o=super.parseObjectData(t,e,i,n);for(let r=0;r<o.length;r++){let a=e[i+r];o[r]._custom=I(a&&a.r&&+a.r,this.resolveDataElementOptions(r+i).radius)}return o}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:n,yScale:o}=e,r=this.getParsed(t),a=n.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:i[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,n){let o=n==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,n),h=r.axis,u=a.axis;for(let d=e;d<e+i;d++){let f=t[d],m=!o&&this.getParsed(d),g={},p=g[h]=o?r.getPixelForDecimal(.5):r.getPixelForValue(m[h]),y=g[u]=o?a.getBasePixel():a.getPixelForValue(m[u]);g.skip=isNaN(p)||isNaN(y),c&&(g.options=l||this.resolveDataElementOptions(d,f.active?"active":n),o&&(g.options.radius=0)),this.updateElement(f,d,g,n)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));let o=n.radius;return e!=="active"&&(n.radius=0),n.radius+=I(i&&i._custom,o),n}};S(Xe,"id","bubble"),S(Xe,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),S(Xe,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function Nu(s,t,e){let i=1,n=1,o=0,r=0;if(t<j){let a=s,l=a+t,c=Math.cos(a),h=Math.sin(a),u=Math.cos(l),d=Math.sin(l),f=(_,w,x)=>je(_,a,l,!0)?1:Math.max(w,w*e,x,x*e),m=(_,w,x)=>je(_,a,l,!0)?-1:Math.min(w,w*e,x,x*e),g=f(0,c,u),p=f(X,h,d),y=m($,c,u),b=m($+X,h,d);i=(g-y)/2,n=(p-b)/2,o=-(g+y)/2,r=-(p+b)/2}return{ratioX:i,ratioY:n,offsetX:o,offsetY:r}}var Gt=class extends pt{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=i;else{let o=l=>+i[l];if(L(i[t])){let{key:l="value"}=this._parsing;o=c=>+Ut(i[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)n._parsed[r]=o(r)}}_getRotation(){return wt(this.options.rotation-90)}_getCircumference(){return wt(this.options.circumference)}_getRotationExtents(){let t=j,e=-j;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let n=this.chart.getDatasetMeta(i).controller,o=n._getRotation(),r=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){let e=this.chart,{chartArea:i}=e,n=this._cachedMeta,o=n.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-r)/2,0),l=Math.min(Br(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:u}=this._getRotationExtents(),{ratioX:d,ratioY:f,offsetX:m,offsetY:g}=Nu(u,h,l),p=(i.width-r)/d,y=(i.height-r)/f,b=Math.max(Math.min(p,y)/2,0),_=An(this.options.radius,b),w=Math.max(_*l,0),x=(_-w)/this._getVisibleDatasetWeightTotal();this.offsetX=m*_,this.offsetY=g*_,n.total=this.calculateTotal(),this.outerRadius=_-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){let i=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/j)}updateElements(t,e,i,n){let o=n==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,u=(a.top+a.bottom)/2,d=o&&c.animateScale,f=d?0:this.innerRadius,m=d?0:this.outerRadius,{sharedOptions:g,includeOptions:p}=this._getSharedOptions(e,n),y=this._getRotation(),b;for(b=0;b<e;++b)y+=this._circumference(b,o);for(b=e;b<e+i;++b){let _=this._circumference(b,o),w=t[b],x={x:h+this.offsetX,y:u+this.offsetY,startAngle:y,endAngle:y+_,circumference:_,outerRadius:m,innerRadius:f};p&&(x.options=g||this.resolveDataElementOptions(b,w.active?"active":n)),y+=_,this.updateElement(w,b,x,n)}}calculateTotal(){let t=this._cachedMeta,e=t.data,i=0,n;for(n=0;n<e.length;n++){let o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(i+=Math.abs(o))}return i}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?j*(Math.abs(t)/e):0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,n=i.data.labels||[],o=Ue(e._parsed[t],i.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0,i=this.chart,n,o,r,a,l;if(!t){for(n=0,o=i.data.datasets.length;n<o;++n)if(i.isDatasetVisible(n)){r=i.getDatasetMeta(n),t=r.data,a=r.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,n=t.length;i<n;++i){let o=this.resolveDataElementOptions(i);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(I(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}};S(Gt,"id","doughnut"),S(Gt,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),S(Gt,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),S(Gt,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:n}}=t.legend.options;return e.labels.map((o,r)=>{let l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}});var Ke=class extends pt{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:n=[],_dataset:o}=e,r=this.chart._animationsDisabled,{start:a,count:l}=$n(e,n,r);this._drawStart=a,this._drawCount=l,jn(e)&&(a=0,l=n.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!o._decimated,i.points=n;let c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(i,void 0,{animated:!r,options:c},t),this.updateElements(n,a,l,t)}updateElements(t,e,i,n){let o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:u}=this._getSharedOptions(e,n),d=r.axis,f=a.axis,{spanGaps:m,segment:g}=this.options,p=we(m)?m:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||o||n==="none",b=e+i,_=t.length,w=e>0&&this.getParsed(e-1);for(let x=0;x<_;++x){let k=t[x],M=y?k:{};if(x<e||x>=b){M.skip=!0;continue}let v=this.getParsed(x),O=A(v[f]),E=M[d]=r.getPixelForValue(v[d],x),C=M[f]=o||O?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,v,l):v[f],x);M.skip=isNaN(E)||isNaN(C)||O,M.stop=x>0&&Math.abs(v[d]-w[d])>p,g&&(M.parsed=v,M.raw=c.data[x]),u&&(M.options=h||this.resolveDataElementOptions(x,k.active?"active":n)),y||this.updateElement(k,x,M,n),w=v}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return i;let o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(i,o,r)/2}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}};S(Ke,"id","line"),S(Ke,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),S(Ke,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});var Oe=class extends pt{constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,n=i.data.labels||[],o=Ue(e._parsed[t].r,i.options.locale);return{label:n[t]||"",value:o}}parseObjectData(t,e,i,n){return Qn.bind(this)(t,e,i,n)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((i,n)=>{let o=this.getParsed(n).r;!isNaN(o)&&this.chart.getDataVisibility(n)&&(o<e.min&&(e.min=o),o>e.max&&(e.max=o))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),o=Math.max(n/2,0),r=Math.max(i.cutoutPercentage?o/100*i.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,i,n){let o=n==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,u=c.yCenter,d=c.getIndexAngle(0)-.5*$,f=d,m,g=360/this.countVisibleElements();for(m=0;m<e;++m)f+=this._computeAngle(m,n,g);for(m=e;m<e+i;m++){let p=t[m],y=f,b=f+this._computeAngle(m,n,g),_=r.getDataVisibility(m)?c.getDistanceFromCenterForValue(this.getParsed(m).r):0;f=b,o&&(l.animateScale&&(_=0),l.animateRotate&&(y=b=d));let w={x:h,y:u,innerRadius:0,outerRadius:_,startAngle:y,endAngle:b,options:this.resolveDataElementOptions(m,p.active?"active":n)};this.updateElement(p,m,w,n)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((i,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?wt(this.resolveDataElementOptions(t,e).angle||i):0}};S(Oe,"id","polarArea"),S(Oe,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),S(Oe,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:n}}=t.legend.options;return e.labels.map((o,r)=>{let l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});var Ps=class extends Gt{};S(Ps,"id","pie"),S(Ps,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});var Je=class extends pt{getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,n){return Qn.bind(this)(t,e,i,n)}update(t){let e=this._cachedMeta,i=e.dataset,n=e.data||[],o=e.iScale.getLabels();if(i.points=n,t!=="resize"){let r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);let a={_loop:!0,_fullLoop:o.length===n.length,options:r};this.updateElement(i,void 0,a,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,i,n){let o=this._cachedMeta.rScale,r=n==="reset";for(let a=e;a<e+i;a++){let l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":n),h=o.getPointPositionForValue(a,this.getParsed(a).r),u=r?o.xCenter:h.x,d=r?o.yCenter:h.y,f={x:u,y:d,angle:h.angle,skip:isNaN(u)||isNaN(d),options:c};this.updateElement(l,a,f,n)}}};S(Je,"id","radar"),S(Je,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),S(Je,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});var Qe=class extends pt{getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:n,yScale:o}=e,r=this.getParsed(t),a=n.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:i[t]||"",value:"("+a+", "+l+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,n=this.chart._animationsDisabled,{start:o,count:r}=$n(e,i,n);if(this._drawStart=o,this._drawCount=r,jn(e)&&(o=0,r=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:a,_dataset:l}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=i;let c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!n,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,o,r,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,n){let o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),u=this.getSharedOptions(h),d=this.includeOptions(n,u),f=r.axis,m=a.axis,{spanGaps:g,segment:p}=this.options,y=we(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",_=e>0&&this.getParsed(e-1);for(let w=e;w<e+i;++w){let x=t[w],k=this.getParsed(w),M=b?x:{},v=A(k[m]),O=M[f]=r.getPixelForValue(k[f],w),E=M[m]=o||v?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,k,l):k[m],w);M.skip=isNaN(O)||isNaN(E)||v,M.stop=w>0&&Math.abs(k[f]-_[f])>y,p&&(M.parsed=k,M.raw=c.data[w]),d&&(M.options=u||this.resolveDataElementOptions(w,x.active?"active":n)),b||this.updateElement(x,w,M,n),_=k}this.updateSharedOptions(u,n,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let a=0;for(let l=e.length-1;l>=0;--l)a=Math.max(a,e[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}let i=t.dataset,n=i.options&&i.options.borderWidth||0;if(!e.length)return n;let o=e[0].size(this.resolveDataElementOptions(0)),r=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,o,r)/2}};S(Qe,"id","scatter"),S(Qe,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),S(Qe,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var Ru=Object.freeze({__proto__:null,BarController:Ge,BubbleController:Xe,DoughnutController:Gt,LineController:Ke,PieController:Ps,PolarAreaController:Oe,RadarController:Je,ScatterController:Qe});function ke(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}var _o=class s{constructor(t){S(this,"options");this.options=t||{}}static override(t){Object.assign(s.prototype,t)}init(){}formats(){return ke()}parse(){return ke()}format(){return ke()}add(){return ke()}diff(){return ke()}startOf(){return ke()}endOf(){return ke()}},Fo={_date:_o};function Wu(s,t,e,i){let{controller:n,data:o,_sorted:r}=s,a=n._cachedMeta.iScale,l=s.dataset&&s.dataset.options?s.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){let c=a._reversePixels?qr:Lt;if(i){if(n._sharedOptions){let h=o[0],u=typeof h.getRange=="function"&&h.getRange(t);if(u){let d=c(o,t,e-u),f=c(o,t,e+u);return{lo:d.lo,hi:f.hi}}}}else{let h=c(o,t,e);if(l){let{vScale:u}=n._cachedMeta,{_parsed:d}=s,f=d.slice(0,h.lo+1).reverse().findIndex(g=>!A(g[u.axis]));h.lo-=Math.max(0,f);let m=d.slice(h.hi).findIndex(g=>!A(g[u.axis]));h.hi+=Math.max(0,m)}return h}}return{lo:0,hi:o.length-1}}function Ys(s,t,e,i,n){let o=s.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){let{index:c,data:h}=o[a],{lo:u,hi:d}=Wu(o[a],t,r,n);for(let f=u;f<=d;++f){let m=h[f];m.skip||i(m,c,f)}}}function zu(s){let t=s.indexOf("x")!==-1,e=s.indexOf("y")!==-1;return function(i,n){let o=t?Math.abs(i.x-n.x):0,r=e?Math.abs(i.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function uo(s,t,e,i,n){let o=[];return!n&&!s.isPointInArea(t)||Ys(s,e,t,function(a,l,c){!n&&!Pt(a,s.chartArea,0)||a.inRange(t.x,t.y,i)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Vu(s,t,e,i){let n=[];function o(r,a,l){let{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],i),{angle:u}=Wn(r,{x:t.x,y:t.y});je(u,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return Ys(s,e,t,o),n}function Hu(s,t,e,i,n,o){let r=[],a=zu(e),l=Number.POSITIVE_INFINITY;function c(h,u,d){let f=h.inRange(t.x,t.y,n);if(i&&!f)return;let m=h.getCenterPoint(n);if(!(!!o||s.isPointInArea(m))&&!f)return;let p=a(t,m);p<l?(r=[{element:h,datasetIndex:u,index:d}],l=p):p===l&&r.push({element:h,datasetIndex:u,index:d})}return Ys(s,e,t,c),r}function fo(s,t,e,i,n,o){return!o&&!s.isPointInArea(t)?[]:e==="r"&&!i?Vu(s,t,e,n):Hu(s,t,e,i,n,o)}function va(s,t,e,i,n){let o=[],r=e==="x"?"inXRange":"inYRange",a=!1;return Ys(s,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),i&&!a?[]:o}var Bu={evaluateInteractionItems:Ys,modes:{index(s,t,e,i){let n=ae(t,s),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?uo(s,n,o,i,r):fo(s,n,o,!1,i,r),l=[];return a.length?(s.getSortedVisibleDatasetMetas().forEach(c=>{let h=a[0].index,u=c.data[h];u&&!u.skip&&l.push({element:u,datasetIndex:c.index,index:h})}),l):[]},dataset(s,t,e,i){let n=ae(t,s),o=e.axis||"xy",r=e.includeInvisible||!1,a=e.intersect?uo(s,n,o,i,r):fo(s,n,o,!1,i,r);if(a.length>0){let l=a[0].datasetIndex,c=s.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(s,t,e,i){let n=ae(t,s),o=e.axis||"xy",r=e.includeInvisible||!1;return uo(s,n,o,i,r)},nearest(s,t,e,i){let n=ae(t,s),o=e.axis||"xy",r=e.includeInvisible||!1;return fo(s,n,o,e.intersect,i,r)},x(s,t,e,i){let n=ae(t,s);return va(s,n,"x",e.intersect,i)},y(s,t,e,i){let n=ae(t,s);return va(s,n,"y",e.intersect,i)}}},yl=["left","top","right","bottom"];function Cs(s,t){return s.filter(e=>e.pos===t)}function Ta(s,t){return s.filter(e=>yl.indexOf(e.pos)===-1&&e.box.axis===t)}function Fs(s,t){return s.sort((e,i)=>{let n=t?i:e,o=t?e:i;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function $u(s){let t=[],e,i,n,o,r,a;for(e=0,i=(s||[]).length;e<i;++e)n=s[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function ju(s){let t={};for(let e of s){let{stack:i,pos:n,stackWeight:o}=e;if(!i||!yl.includes(n))continue;let r=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Uu(s,t){let e=ju(s),{vBoxMaxWidth:i,hBoxMaxHeight:n}=t,o,r,a;for(o=0,r=s.length;o<r;++o){a=s[o];let{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*i:l&&t.availableWidth,a.height=n):(a.width=i,a.height=h?h*n:l&&t.availableHeight)}return e}function Yu(s){let t=$u(s),e=Fs(t.filter(c=>c.box.fullSize),!0),i=Fs(Cs(t,"left"),!0),n=Fs(Cs(t,"right")),o=Fs(Cs(t,"top"),!0),r=Fs(Cs(t,"bottom")),a=Ta(t,"x"),l=Ta(t,"y");return{fullSize:e,leftAndTop:i.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Cs(t,"chartArea"),vertical:i.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Oa(s,t,e,i){return Math.max(s[e],t[e])+Math.max(s[i],t[i])}function bl(s,t){s.top=Math.max(s.top,t.top),s.left=Math.max(s.left,t.left),s.bottom=Math.max(s.bottom,t.bottom),s.right=Math.max(s.right,t.right)}function Zu(s,t,e,i){let{pos:n,box:o}=e,r=s.maxPadding;if(!L(n)){e.size&&(s[n]-=e.size);let u=i[e.stack]||{size:0,count:1};u.size=Math.max(u.size,e.horizontal?o.height:o.width),e.size=u.size/u.count,s[n]+=e.size}o.getPadding&&bl(r,o.getPadding());let a=Math.max(0,t.outerWidth-Oa(r,s,"left","right")),l=Math.max(0,t.outerHeight-Oa(r,s,"top","bottom")),c=a!==s.w,h=l!==s.h;return s.w=a,s.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function qu(s){let t=s.maxPadding;function e(i){let n=Math.max(t[i]-s[i],0);return s[i]+=n,n}s.y+=e("top"),s.x+=e("left"),e("right"),e("bottom")}function Gu(s,t){let e=t.maxPadding;function i(n){let o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return i(s?["left","right"]:["top","bottom"])}function Ns(s,t,e,i){let n=[],o,r,a,l,c,h;for(o=0,r=s.length,c=0;o<r;++o){a=s[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Gu(a.horizontal,t));let{same:u,other:d}=Zu(t,e,a,i);c|=u&&n.length,h=h||d,l.fullSize||n.push(a)}return c&&Ns(n,t,e,i)||h}function Zi(s,t,e,i,n){s.top=e,s.left=t,s.right=t+i,s.bottom=e+n,s.width=i,s.height=n}function Da(s,t,e,i){let n=e.padding,{x:o,y:r}=t;for(let a of s){let l=a.box,c=i[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){let u=t.w*h,d=c.size||l.height;Be(c.start)&&(r=c.start),l.fullSize?Zi(l,n.left,r,e.outerWidth-n.right-n.left,d):Zi(l,t.left+c.placed,r,u,d),c.start=r,c.placed+=u,r=l.bottom}else{let u=t.h*h,d=c.size||l.width;Be(c.start)&&(o=c.start),l.fullSize?Zi(l,o,n.top,d,e.outerHeight-n.bottom-n.top):Zi(l,o,t.top+c.placed,d,u),c.start=o,c.placed+=u,o=l.right}}t.x=o,t.y=r}var lt={addBox(s,t){s.boxes||(s.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},s.boxes.push(t)},removeBox(s,t){let e=s.boxes?s.boxes.indexOf(t):-1;e!==-1&&s.boxes.splice(e,1)},configure(s,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(s,t,e,i){if(!s)return;let n=rt(s.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Yu(s.boxes),l=a.vertical,c=a.horizontal;z(s.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});let h=l.reduce((g,p)=>p.box.options&&p.box.options.display===!1?g:g+1,0)||1,u=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),d=Object.assign({},n);bl(d,rt(i));let f=Object.assign({maxPadding:d,w:o,h:r,x:n.left,y:n.top},n),m=Uu(l.concat(c),u);Ns(a.fullSize,f,u,m),Ns(l,f,u,m),Ns(c,f,u,m)&&Ns(l,f,u,m),qu(f),Da(a.leftAndTop,f,u,m),f.x+=f.w,f.y+=f.h,Da(a.rightAndBottom,f,u,m),s.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},z(a.chartArea,g=>{let p=g.box;Object.assign(p,s.chartArea),p.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}},sn=class{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,n){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):i)}}isAttached(t){return!0}updateConfig(t){}},wo=class extends sn{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}},Qi="$chartjs",Xu={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Ea=s=>s===null||s==="";function Ku(s,t){let e=s.style,i=s.getAttribute("height"),n=s.getAttribute("width");if(s[Qi]={initial:{height:i,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Ea(n)){let o=eo(s,"width");o!==void 0&&(s.width=o)}if(Ea(i))if(s.style.height==="")s.height=s.width/(t||2);else{let o=eo(s,"height");o!==void 0&&(s.height=o)}return s}var xl=ua?{passive:!0}:!1;function Ju(s,t,e){s&&s.addEventListener(t,e,xl)}function Qu(s,t,e){s&&s.canvas&&s.canvas.removeEventListener(t,e,xl)}function td(s,t){let e=Xu[s.type]||s.type,{x:i,y:n}=ae(s,t);return{type:e,chart:t,native:s,x:i!==void 0?i:null,y:n!==void 0?n:null}}function nn(s,t){for(let e of s)if(e===t||e.contains(t))return!0}function ed(s,t,e){let i=s.canvas,n=new MutationObserver(o=>{let r=!1;for(let a of o)r=r||nn(a.addedNodes,i),r=r&&!nn(a.removedNodes,i);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function sd(s,t,e){let i=s.canvas,n=new MutationObserver(o=>{let r=!1;for(let a of o)r=r||nn(a.removedNodes,i),r=r&&!nn(a.addedNodes,i);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}var $s=new Map,Ia=0;function _l(){let s=window.devicePixelRatio;s!==Ia&&(Ia=s,$s.forEach((t,e)=>{e.currentDevicePixelRatio!==s&&t()}))}function id(s,t){$s.size||window.addEventListener("resize",_l),$s.set(s,t)}function nd(s){$s.delete(s),$s.size||window.removeEventListener("resize",_l)}function od(s,t,e){let i=s.canvas,n=i&&Ui(i);if(!n)return;let o=Bn((a,l)=>{let c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{let l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),id(s,o),r}function mo(s,t,e){e&&e.disconnect(),t==="resize"&&nd(s)}function rd(s,t,e){let i=s.canvas,n=Bn(o=>{s.ctx!==null&&e(td(o,s))},s);return Ju(i,t,n),n}var So=class extends sn{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(Ku(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[Qi])return!1;let i=e[Qi].initial;["height","width"].forEach(o=>{let r=i[o];A(r)?e.removeAttribute(o):e.setAttribute(o,r)});let n=i.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[Qi],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let n=t.$proxies||(t.$proxies={}),r={attach:ed,detach:sd,resize:od}[e]||rd;n[e]=r(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),n=i[e];if(!n)return;({attach:mo,detach:mo,resize:mo}[e]||Qu)(t,e,n),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,n){return ha(t,e,i,n)}isAttached(t){let e=t&&Ui(t);return!!(e&&e.isConnected)}};function ad(s){return!ji()||typeof OffscreenCanvas<"u"&&s instanceof OffscreenCanvas?wo:So}var yt=class{constructor(){S(this,"x");S(this,"y");S(this,"active",!1);S(this,"options");S(this,"$animations")}tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return we(this.x)&&we(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let n={};return t.forEach(o=>{n[o]=i[o]&&i[o].active()?i[o]._to:this[o]}),n}};S(yt,"defaults",{}),S(yt,"defaultRoutes");function ld(s,t){let e=s.options.ticks,i=cd(s),n=Math.min(e.maxTicksLimit||i,i),o=e.major.enabled?ud(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return dd(t,c,o,r/n),c;let h=hd(o,t,n);if(r>0){let u,d,f=r>1?Math.round((l-a)/(r-1)):null;for(qi(t,c,h,A(f)?0:a-f,a),u=0,d=r-1;u<d;u++)qi(t,c,h,o[u],o[u+1]);return qi(t,c,h,l,A(f)?t.length:l+f),c}return qi(t,c,h),c}function cd(s){let t=s.options.offset,e=s._tickSize(),i=s._length/e+(t?0:1),n=s._maxLength/e;return Math.floor(Math.min(i,n))}function hd(s,t,e){let i=fd(s),n=t.length/e;if(!i)return Math.max(n,1);let o=Ur(i);for(let r=0,a=o.length-1;r<a;r++){let l=o[r];if(l>n)return l}return Math.max(n,1)}function ud(s){let t=[],e,i;for(e=0,i=s.length;e<i;e++)s[e].major&&t.push(e);return t}function dd(s,t,e,i){let n=0,o=e[0],r;for(i=Math.ceil(i),r=0;r<s.length;r++)r===o&&(t.push(s[r]),n++,o=e[n*i])}function qi(s,t,e,i,n){let o=I(i,0),r=Math.min(I(n,s.length),s.length),a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-i,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(s[c]),a++,h=Math.round(o+a*e))}function fd(s){let t=s.length,e,i;if(t<2)return!1;for(i=s[0],e=1;e<t;++e)if(s[e]-s[e-1]!==i)return!1;return i}var md=s=>s==="left"?"right":s==="right"?"left":s,Ca=(s,t,e)=>t==="top"||t==="left"?s[t]+e:s[t]-e,Fa=(s,t)=>Math.min(t||s,s);function Aa(s,t){let e=[],i=s.length/t,n=s.length,o=0;for(;o<n;o+=i)e.push(s[Math.floor(o)]);return e}function gd(s,t,e){let i=s.ticks.length,n=Math.min(t,i-1),o=s._startPixel,r=s._endPixel,a=1e-6,l=s.getPixelForTick(n),c;if(!(e&&(i===1?c=Math.max(l-o,r-l):t===0?c=(s.getPixelForTick(1)-l)/2:c=(l-s.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function pd(s,t){z(s,e=>{let i=e.gc,n=i.length/2,o;if(n>t){for(o=0;o<n;++o)delete e.data[i[o]];i.splice(0,n)}})}function As(s){return s.drawTicks?s.tickLength:0}function La(s,t){if(!s.display)return 0;let e=Q(s.font,t),i=rt(s.padding);return(B(s.text)?s.text.length:1)*e.lineHeight+i.height}function yd(s,t){return Yt(s,{scale:t,type:"scale"})}function bd(s,t,e){return Yt(s,{tick:e,index:t,type:"tick"})}function xd(s,t,e){let i=zi(s);return(e&&t!=="right"||!e&&t==="right")&&(i=md(i)),i}function _d(s,t,e,i){let{top:n,left:o,bottom:r,right:a,chart:l}=s,{chartArea:c,scales:h}=l,u=0,d,f,m,g=r-n,p=a-o;if(s.isHorizontal()){if(f=ot(i,o,a),L(e)){let y=Object.keys(e)[0],b=e[y];m=h[y].getPixelForValue(b)+g-t}else e==="center"?m=(c.bottom+c.top)/2+g-t:m=Ca(s,e,t);d=a-o}else{if(L(e)){let y=Object.keys(e)[0],b=e[y];f=h[y].getPixelForValue(b)-p+t}else e==="center"?f=(c.left+c.right)/2-p+t:f=Ca(s,e,t);m=ot(i,r,n),u=e==="left"?-X:X}return{titleX:f,titleY:m,maxWidth:d,rotation:u}}var Ee=class s extends yt{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:n}=this;return t=ut(t,Number.POSITIVE_INFINITY),e=ut(e,Number.NEGATIVE_INFINITY),i=ut(i,Number.POSITIVE_INFINITY),n=ut(n,Number.NEGATIVE_INFINITY),{min:ut(t,i),max:ut(e,n),minDefined:Z(t),maxDefined:Z(e)}}getMinMax(t){let{min:e,max:i,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:i};let a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(i=Math.max(i,r.max));return e=o&&e>i?i:e,i=n&&e>i?e:i,{min:ut(e,ut(i,e)),max:ut(i,ut(e,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){H(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=na(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=a<this.ticks.length;this._convertTicksToLabels(l?Aa(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=ld(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,i;this.isHorizontal()?(e=this.left,i=this.right):(e=this.top,i=this.bottom,t=!t),this._startPixel=e,this._endPixel=i,this._reversePixels=t,this._length=i-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){H(this.options.afterUpdate,[this])}beforeSetDimensions(){H(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){H(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),H(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){H(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e=this.options.ticks,i,n,o;for(i=0,n=t.length;i<n;i++)o=t[i],o.label=H(e.callback,[o.value,i,t],this)}afterTickToLabelConversion(){H(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){H(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t=this.options,e=t.ticks,i=Fa(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation,r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||i<=1||!this.isHorizontal()){this.labelRotation=n;return}let h=this._getLabelSizes(),u=h.widest.width,d=h.highest.height,f=tt(this.chart.width-u,0,this.maxWidth);a=t.offset?this.maxWidth/i:f/(i-1),u+6>a&&(a=f/(i-(t.offset?.5:1)),l=this.maxHeight-As(t.grid)-e.padding-La(t.title,this.chart.options.font),c=Math.sqrt(u*u+d*d),r=Ri(Math.min(Math.asin(tt((h.highest.height+6)/a,-1,1)),Math.asin(tt(l/c,-1,1))-Math.asin(tt(d/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){H(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){H(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){let l=La(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=As(o)+l):(t.height=this.maxHeight,t.width=As(o)+l),i.display&&this.ticks.length){let{first:c,last:h,widest:u,highest:d}=this._getLabelSizes(),f=i.padding*2,m=wt(this.labelRotation),g=Math.cos(m),p=Math.sin(m);if(a){let y=i.mirror?0:p*u.width+g*d.height;t.height=Math.min(this.maxHeight,t.height+y+f)}else{let y=i.mirror?0:g*u.width+p*d.height;t.width=Math.min(this.maxWidth,t.width+y+f)}this._calculatePadding(c,h,p,g)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,n){let{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){let h=this.getPixelForTick(0)-this.left,u=this.right-this.getPixelForTick(this.ticks.length-1),d=0,f=0;l?c?(d=n*t.width,f=i*e.height):(d=i*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?d=t.width:o!=="inner"&&(d=t.width/2,f=e.width/2),this.paddingLeft=Math.max((d-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-u+r)*this.width/(this.width-u),0)}else{let h=e.height/2,u=t.height/2;o==="start"?(h=0,u=t.height):o==="end"&&(h=e.height,u=0),this.paddingTop=h+r,this.paddingBottom=u+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){H(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,i;for(e=0,i=t.length;e<i;e++)A(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=Aa(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/Fa(e,i)),c=0,h=0,u,d,f,m,g,p,y,b,_,w,x;for(u=0;u<e;u+=l){if(m=t[u].label,g=this._resolveTickFontOptions(u),n.font=p=g.string,y=o[p]=o[p]||{data:{},gc:[]},b=g.lineHeight,_=w=0,!A(m)&&!B(m))_=vs(n,y.data,y.gc,_,m),w=b;else if(B(m))for(d=0,f=m.length;d<f;++d)x=m[d],!A(x)&&!B(x)&&(_=vs(n,y.data,y.gc,_,x),w+=b);r.push(_),a.push(w),c=Math.max(_,c),h=Math.max(w,h)}pd(o,e);let k=r.indexOf(c),M=a.indexOf(h),v=O=>({width:r[O]||0,height:a[O]||0});return{first:v(0),last:v(e-1),widest:v(k),highest:v(M),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return Zr(this._alignToPixels?ne(this.chart,e,0):e)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){let e=this.ticks||[];if(t>=0&&t<e.length){let i=e[t];return i.$context||(i.$context=bd(this.getContext(),t,i))}return this.$context||(this.$context=yd(this.chart.getContext(),this))}_tickSize(){let t=this.options.ticks,e=wt(this.labelRotation),i=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*i>a*n?a/i:l/n:l*n<a*i?l/i:a/n}_isVisible(){let t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e=this.axis,i=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),u=this.ticks.length+(l?1:0),d=As(o),f=[],m=a.setContext(this.getContext()),g=m.display?m.width:0,p=g/2,y=function(Y){return ne(i,Y,g)},b,_,w,x,k,M,v,O,E,C,P,nt;if(r==="top")b=y(this.bottom),M=this.bottom-d,O=b-p,C=y(t.top)+p,nt=t.bottom;else if(r==="bottom")b=y(this.top),C=t.top,nt=y(t.bottom)-p,M=b+p,O=this.top+d;else if(r==="left")b=y(this.right),k=this.right-d,v=b-p,E=y(t.left)+p,P=t.right;else if(r==="right")b=y(this.left),E=t.left,P=y(t.right)-p,k=b+p,v=this.left+d;else if(e==="x"){if(r==="center")b=y((t.top+t.bottom)/2+.5);else if(L(r)){let Y=Object.keys(r)[0],J=r[Y];b=y(this.chart.scales[Y].getPixelForValue(J))}C=t.top,nt=t.bottom,M=b+p,O=M+d}else if(e==="y"){if(r==="center")b=y((t.left+t.right)/2);else if(L(r)){let Y=Object.keys(r)[0],J=r[Y];b=y(this.chart.scales[Y].getPixelForValue(J))}k=b-p,v=k-d,E=t.left,P=t.right}let gt=I(n.ticks.maxTicksLimit,u),V=Math.max(1,Math.ceil(u/gt));for(_=0;_<u;_+=V){let Y=this.getContext(_),J=o.setContext(Y),Tt=a.setContext(Y),at=J.lineWidth,Pe=J.color,Mi=Tt.dash||[],Ne=Tt.dashOffset,xs=J.tickWidth,pe=J.tickColor,_s=J.tickBorderDash||[],ye=J.tickBorderDashOffset;w=gd(this,_,l),w!==void 0&&(x=ne(i,w,at),c?k=v=E=P=x:M=O=C=nt=x,f.push({tx1:k,ty1:M,tx2:v,ty2:O,x1:E,y1:C,x2:P,y2:nt,width:at,color:Pe,borderDash:Mi,borderDashOffset:Ne,tickWidth:xs,tickColor:pe,tickBorderDash:_s,tickBorderDashOffset:ye}))}return this._ticksLength=u,this._borderValue=b,f}_computeLabelItems(t){let e=this.axis,i=this.options,{position:n,ticks:o}=i,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:u}=o,d=As(i.grid),f=d+h,m=u?-h:f,g=-wt(this.labelRotation),p=[],y,b,_,w,x,k,M,v,O,E,C,P,nt="middle";if(n==="top")k=this.bottom-m,M=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+m,M=this._getXAxisLabelAlignment();else if(n==="left"){let V=this._getYAxisLabelAlignment(d);M=V.textAlign,x=V.x}else if(n==="right"){let V=this._getYAxisLabelAlignment(d);M=V.textAlign,x=V.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+f;else if(L(n)){let V=Object.keys(n)[0],Y=n[V];k=this.chart.scales[V].getPixelForValue(Y)+f}M=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-f;else if(L(n)){let V=Object.keys(n)[0],Y=n[V];x=this.chart.scales[V].getPixelForValue(Y)}M=this._getYAxisLabelAlignment(d).textAlign}e==="y"&&(l==="start"?nt="top":l==="end"&&(nt="bottom"));let gt=this._getLabelSizes();for(y=0,b=a.length;y<b;++y){_=a[y],w=_.label;let V=o.setContext(this.getContext(y));v=this.getPixelForTick(y)+o.labelOffset,O=this._resolveTickFontOptions(y),E=O.lineHeight,C=B(w)?w.length:1;let Y=C/2,J=V.color,Tt=V.textStrokeColor,at=V.textStrokeWidth,Pe=M;r?(x=v,M==="inner"&&(y===b-1?Pe=this.options.reverse?"left":"right":y===0?Pe=this.options.reverse?"right":"left":Pe="center"),n==="top"?c==="near"||g!==0?P=-C*E+E/2:c==="center"?P=-gt.highest.height/2-Y*E+E:P=-gt.highest.height+E/2:c==="near"||g!==0?P=E/2:c==="center"?P=gt.highest.height/2-Y*E:P=gt.highest.height-C*E,u&&(P*=-1),g!==0&&!V.showLabelBackdrop&&(x+=E/2*Math.sin(g))):(k=v,P=(1-C)*E/2);let Mi;if(V.showLabelBackdrop){let Ne=rt(V.backdropPadding),xs=gt.heights[y],pe=gt.widths[y],_s=P-Ne.top,ye=0-Ne.left;switch(nt){case"middle":_s-=xs/2;break;case"bottom":_s-=xs;break}switch(M){case"center":ye-=pe/2;break;case"right":ye-=pe;break;case"inner":y===b-1?ye-=pe:y>0&&(ye-=pe/2);break}Mi={left:ye,top:_s,width:pe+Ne.width,height:xs+Ne.height,color:V.backdropColor}}p.push({label:w,font:O,textOffset:P,options:{rotation:g,color:J,strokeColor:Tt,strokeWidth:at,textAlign:Pe,textBaseline:nt,translation:[x,k],backdrop:Mi}})}return p}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-wt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){let{position:e,ticks:{crossAlign:i,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width,c,h;return e==="left"?n?(h=this.right+o,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,n,o,r),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){let e=this.options.grid,i=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),o,r,a=(l,c,h)=>{!h.width||!h.color||(i.save(),i.lineWidth=h.width,i.strokeStyle=h.color,i.setLineDash(h.borderDash||[]),i.lineDashOffset=h.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){let l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){let{chart:t,ctx:e,options:{border:i,grid:n}}=this,o=i.setContext(this.getContext()),r=i.display?o.width:0;if(!r)return;let a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue,c,h,u,d;this.isHorizontal()?(c=ne(t,this.left,r)-r/2,h=ne(t,this.right,a)+a/2,u=d=l):(u=ne(t,this.top,r)-r/2,d=ne(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,u),e.lineTo(h,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;let i=this.ctx,n=this._computeLabelArea();n&&Ds(i,n);let o=this.getLabelItems(t);for(let r of o){let a=r.options,l=r.font,c=r.label,h=r.textOffset;oe(i,c,0,h,l,a)}n&&Es(i)}drawTitle(){let{ctx:t,options:{position:e,title:i,reverse:n}}=this;if(!i.display)return;let o=Q(i.font),r=rt(i.padding),a=i.align,l=o.lineHeight/2;e==="bottom"||e==="center"||L(e)?(l+=r.bottom,B(i.text)&&(l+=o.lineHeight*(i.text.length-1))):l+=r.top;let{titleX:c,titleY:h,maxWidth:u,rotation:d}=_d(this,l,e,a);oe(t,i.text,0,0,o,{color:i.color,maxWidth:u,rotation:d,textAlign:xd(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=I(t.grid&&t.grid.z,-1),n=I(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==s.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:i,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){let e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",n=[],o,r;for(o=0,r=e.length;o<r;++o){let a=e[o];a[i]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){let e=this.options.ticks.setContext(this.getContext(t));return Q(e.font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}},ss=class{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){let e=Object.getPrototypeOf(t),i;kd(e)&&(i=this.register(e));let n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,wd(t,r,i),this.override&&U.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,n=this.scope;i in e&&delete e[i],n&&i in U[n]&&(delete U[n][i],this.override&&delete ie[i])}};function wd(s,t,e){let i=ze(Object.create(null),[e?U.get(e):{},U.get(t),s.defaults]);U.set(t,i),s.defaultRoutes&&Sd(t,s.defaultRoutes),s.descriptors&&U.describe(t,s.descriptors)}function Sd(s,t){Object.keys(t).forEach(e=>{let i=e.split("."),n=i.pop(),o=[s].concat(i).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");U.route(o,n,l,a)})}function kd(s){return"id"in s&&"defaults"in s}var ko=class{constructor(){this.controllers=new ss(pt,"datasets",!0),this.elements=new ss(yt,"elements"),this.plugins=new ss(Object,"plugins"),this.scales=new ss(Ee,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(n=>{let o=i||this._getRegistryForType(n);i||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):z(n,r=>{let a=i||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,i){let n=Ni(t);H(i["before"+n],[],i),e[t](i),H(i["after"+n],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return n}},zt=new ko,Mo=class{constructor(){this._init=[]}notify(t,e,i,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,i);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,i,n){n=n||{};for(let o of t){let r=o.plugin,a=r[i],l=[e,n,o.options];if(H(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){A(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,n=I(i.options&&i.options.plugins,{}),o=Md(i);return n===!1&&!e?[]:Td(t,o,n,e)}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,i),t,"stop"),this._notify(n(i,e),t,"start")}};function Md(s){let t={},e=[],i=Object.keys(zt.plugins.items);for(let o=0;o<i.length;o++)e.push(zt.getPlugin(i[o]));let n=s.plugins||[];for(let o=0;o<n.length;o++){let r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function vd(s,t){return!t&&s===!1?null:s===!0?{}:s}function Td(s,{plugins:t,localIds:e},i,n){let o=[],r=s.getContext();for(let a of t){let l=a.id,c=vd(i[l],n);c!==null&&o.push({plugin:a,options:Od(s.config,{plugin:a,local:e[l]},c,r)})}return o}function Od(s,{plugin:t,local:e},i,n){let o=s.pluginScopeKeys(t),r=s.getOptionScopes(i,o);return e&&t.defaults&&r.push(t.defaults),s.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function vo(s,t){let e=U.datasets[s]||{};return((t.datasets||{})[s]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Dd(s,t){let e=s;return s==="_index_"?e=t:s==="_value_"&&(e=t==="x"?"y":"x"),e}function Ed(s,t){return s===t?"_index_":"_value_"}function Pa(s){if(s==="x"||s==="y"||s==="r")return s}function Id(s){if(s==="top"||s==="bottom")return"x";if(s==="left"||s==="right")return"y"}function To(s,...t){if(Pa(s))return s;for(let e of t){let i=e.axis||Id(e.position)||s.length>1&&Pa(s[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${s}' axis. Please provide 'axis' or 'position' option.`)}function Na(s,t,e){if(e[t+"AxisID"]===s)return{axis:t}}function Cd(s,t){if(t.data&&t.data.datasets){let e=t.data.datasets.filter(i=>i.xAxisID===s||i.yAxisID===s);if(e.length)return Na(s,"x",e[0])||Na(s,"y",e[0])}return{}}function Fd(s,t){let e=ie[s.type]||{scales:{}},i=t.scales||{},n=vo(s.type,t),o=Object.create(null);return Object.keys(i).forEach(r=>{let a=i[r];if(!L(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);let l=To(r,a,Cd(r,s),U.scales[a.type]),c=Ed(l,n),h=e.scales||{};o[r]=He(Object.create(null),[{axis:l},a,h[l],h[c]])}),s.data.datasets.forEach(r=>{let a=r.type||s.type,l=r.indexAxis||vo(a,t),h=(ie[a]||{}).scales||{};Object.keys(h).forEach(u=>{let d=Dd(u,l),f=r[d+"AxisID"]||d;o[f]=o[f]||Object.create(null),He(o[f],[{axis:d},i[f],h[u]])})}),Object.keys(o).forEach(r=>{let a=o[r];He(a,[U.scales[a.type],U.scale])}),o}function wl(s){let t=s.options||(s.options={});t.plugins=I(t.plugins,{}),t.scales=Fd(s,t)}function Sl(s){return s=s||{},s.datasets=s.datasets||[],s.labels=s.labels||[],s}function Ad(s){return s=s||{},s.data=Sl(s.data),wl(s),s}var Ra=new Map,kl=new Set;function Gi(s,t){let e=Ra.get(s);return e||(e=t(),Ra.set(s,e),kl.add(e)),e}var Ls=(s,t,e)=>{let i=Ut(t,e);i!==void 0&&s.add(i)},Oo=class{constructor(t){this._config=Ad(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Sl(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),wl(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Gi(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return Gi(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return Gi(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return Gi(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,n=i.get(t);return(!n||e)&&(n=new Map,i.set(t,n)),n}getOptionScopes(t,e,i){let{options:n,type:o}=this,r=this._cachedScopes(t,i),a=r.get(e);if(a)return a;let l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(u=>Ls(l,t,u))),h.forEach(u=>Ls(l,n,u)),h.forEach(u=>Ls(l,ie[o]||{},u)),h.forEach(u=>Ls(l,U,u)),h.forEach(u=>Ls(l,Vi,u))});let c=Array.from(l);return c.length===0&&c.push(Object.create(null)),kl.has(e)&&r.set(e,c),c}chartOptionScopes(){let{options:t,type:e}=this;return[t,ie[e]||{},U.datasets[e]||{},{type:e},U,Vi]}resolveNamedOptions(t,e,i,n=[""]){let o={$shared:!0},{resolver:r,subPrefixes:a}=Wa(this._resolverCache,t,n),l=r;if(Pd(r,e)){o.$shared=!1,i=$t(i)?i():i;let c=this.createResolver(t,i,a);l=_e(r,i,c)}for(let c of e)o[c]=l[c];return o}createResolver(t,e,i=[""],n){let{resolver:o}=Wa(this._resolverCache,t,i);return L(e)?_e(o,e,void 0,n):o}};function Wa(s,t,e){let i=s.get(t);i||(i=new Map,s.set(t,i));let n=e.join(),o=i.get(n);return o||(o={resolver:$i(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},i.set(n,o)),o}var Ld=s=>L(s)&&Object.getOwnPropertyNames(s).some(t=>$t(s[t]));function Pd(s,t){let{isScriptable:e,isIndexable:i}=Xn(s);for(let n of t){let o=e(n),r=i(n),a=(r||o)&&s[n];if(o&&($t(a)||Ld(a))||r&&B(a))return!0}return!1}var Nd="4.4.9",Rd=["top","bottom","left","right","chartArea"];function za(s,t){return s==="top"||s==="bottom"||Rd.indexOf(s)===-1&&t==="x"}function Va(s,t){return function(e,i){return e[s]===i[s]?e[t]-i[t]:e[s]-i[s]}}function Ha(s){let t=s.chart,e=t.options.animation;t.notifyPlugins("afterRender"),H(e&&e.onComplete,[s],t)}function Wd(s){let t=s.chart,e=t.options.animation;H(e&&e.onProgress,[s],t)}function Ml(s){return ji()&&typeof s=="string"?s=document.getElementById(s):s&&s.length&&(s=s[0]),s&&s.canvas&&(s=s.canvas),s}var tn={},Ba=s=>{let t=Ml(s);return Object.values(tn).filter(e=>e.canvas===t).pop()};function zd(s,t,e){let i=Object.keys(s);for(let n of i){let o=+n;if(o>=t){let r=s[n];delete s[n],(e>0||o>t)&&(s[o+e]=r)}}}function Vd(s,t,e,i){return!e||s.type==="mouseout"?null:i?t:s}var St=class{static register(...t){zt.add(...t),$a()}static unregister(...t){zt.remove(...t),$a()}constructor(t,e){let i=this.config=new Oo(e),n=Ml(t),o=Ba(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");let r=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||ad(n)),this.platform.updateConfig(i);let a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Hr(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Mo,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Jr(u=>this.update(u),r.resizeDelay||0),this._dataChanges=[],tn[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Zt.listen(this,"complete",Ha),Zt.listen(this,"progress",Wd),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:n,_aspectRatio:o}=this;return A(t)?e&&o?o:n?i/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return zt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():to(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Zn(this.canvas,this.ctx),this}stop(){return Zt.stop(this),this}resize(t,e){Zt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,n=this.canvas,o=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,to(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),H(i.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){let e=this.options.scales||{};z(e,(i,n)=>{i.id=n})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,n=Object.keys(i).reduce((r,a)=>(r[a]=!1,r),{}),o=[];e&&(o=o.concat(Object.keys(e).map(r=>{let a=e[r],l=To(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),z(o,r=>{let a=r.options,l=a.id,c=To(l,a),h=I(a.type,r.dtype);(a.position===void 0||za(a.position,c)!==za(r.dposition))&&(a.position=r.dposition),n[l]=!0;let u=null;if(l in i&&i[l].type===h)u=i[l];else{let d=zt.getScale(h);u=new d({id:l,type:h,ctx:this.ctx,chart:this}),i[u.id]=u}u.init(a,t)}),z(n,(r,a)=>{r||delete i[a]}),z(i,r=>{lt.configure(this,r,r.options),lt.addBox(this,r)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((n,o)=>n.index-o.index),i>e){for(let n=e;n<i;++n)this._destroyDatasetMeta(n);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(Va("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((i,n)=>{e.filter(o=>o===i._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){let t=[],e=this.data.datasets,i,n;for(this._removeUnreferencedMetasets(),i=0,n=e.length;i<n;i++){let o=e[i],r=this.getDatasetMeta(i),a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(i),r=this.getDatasetMeta(i)),r.type=a,r.indexAxis=o.indexAxis||vo(a,this.options),r.order=o.order||0,r.index=i,r.label=""+o.label,r.visible=this.isDatasetVisible(i),r.controller)r.controller.updateIndex(i),r.controller.linkScales();else{let l=zt.getController(a),{datasetElementType:c,dataElementType:h}=U.datasets[a];Object.assign(l,{dataElementType:zt.getElement(h),datasetElementType:c&&zt.getElement(c)}),r.controller=new l(this,i),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){z(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;let o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){let{controller:u}=this.getDatasetMeta(c),d=!n&&o.indexOf(u)===-1;u.buildOrUpdateElements(d),r=Math.max(+u.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),n||z(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Va("z","_idx"));let{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){z(this.scales,t=>{lt.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!Ln(e,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(let{method:i,start:n,count:o}of e){let r=i==="_removeElements"?-o:o;zd(t,n,r)}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=i(0);for(let o=1;o<e;o++)if(!Ln(n,i(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;lt.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],z(this.boxes,n=>{i&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,i=this.data.datasets.length;e<i;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,$t(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),n={meta:i,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(i.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Zt.has(this)?this.attached&&!Zt.running(this)&&Zt.start(this):(this.draw(),Ha({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:i,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e=this._sortedMetasets,i=[],n,o;for(n=0,o=e.length;n<o;++n){let r=e[n];(!t||r.visible)&&i.push(r)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i={meta:t,index:t.index,cancelable:!0},n=ro(this,t);this.notifyPlugins("beforeDatasetDraw",i)!==!1&&(n&&Ds(e,n),t.controller.draw(),n&&Es(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return Pt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,n){let o=Bu.modes[e];return typeof o=="function"?o(this,t,i,n):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,n=i.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(n)),n}getContext(){return this.$context||(this.$context=Yt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!e.hidden}setDatasetVisibility(t,e){let i=this.getDatasetMeta(t);i.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let n=i?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Be(e)?(o.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),r.update(o,{visible:i}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),Zt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Zn(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete tn[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};z(this.options.events,o=>i(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});let t=this._responsiveListeners,e=this.platform,i=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)},r,a=()=>{n("attach",a),this.attached=!0,this.resize(),i("resize",o),i("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),i("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){z(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},z(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let n=i?"set":"remove",o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];let c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:o,index:r})=>{let a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!Ts(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,i){let n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(u=>h.datasetIndex===u.datasetIndex&&h.index===u.index)),r=o(e,t),a=i?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,n)===!1)return;let o=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,n),(o||i.changed)&&this.render(),this}_handleEvent(t,e,i){let{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,i,r),l=jr(t),c=Vd(t,this._lastEvent,i,l);i&&(this._lastEvent=null,H(o.onHover,[t,a,this],this),l&&H(o.onClick,[t,a,this],this));let h=!Ts(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,i,n){if(t.type==="mouseout")return[];if(!i)return e;let o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}};S(St,"defaults",U),S(St,"instances",tn),S(St,"overrides",ie),S(St,"registry",zt),S(St,"version",Nd),S(St,"getChart",Ba);function $a(){return z(St.instances,s=>s._plugins.invalidate())}function Hd(s,t,e){let{startAngle:i,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=t,c=n/a;s.beginPath(),s.arc(o,r,a,i-c,e+c),l>n?(c=n/l,s.arc(o,r,l,e+c,i-c,!0)):s.arc(o,r,n,e+X,i-X),s.closePath(),s.clip()}function Bd(s){return Bi(s,["outerStart","outerEnd","innerStart","innerEnd"])}function $d(s,t,e,i){let n=Bd(s.options.borderRadius),o=(e-t)/2,r=Math.min(o,i*t/2),a=l=>{let c=(e-Math.min(o,l))*i/2;return tt(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:tt(n.innerStart,0,r),innerEnd:tt(n.innerEnd,0,r)}}function qe(s,t,e,i){return{x:e+s*Math.cos(t),y:i+s*Math.sin(t)}}function on(s,t,e,i,n,o){let{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,u=Math.max(t.outerRadius+i+e-c,0),d=h>0?h+i+e+c:0,f=0,m=n-l;if(i){let V=h>0?h-i:0,Y=u>0?u-i:0,J=(V+Y)/2,Tt=J!==0?m*J/(J+i):m;f=(m-Tt)/2}let g=Math.max(.001,m*u-e/$)/u,p=(m-g)/2,y=l+p+f,b=n-p-f,{outerStart:_,outerEnd:w,innerStart:x,innerEnd:k}=$d(t,d,u,b-y),M=u-_,v=u-w,O=y+_/M,E=b-w/v,C=d+x,P=d+k,nt=y+x/C,gt=b-k/P;if(s.beginPath(),o){let V=(O+E)/2;if(s.arc(r,a,u,O,V),s.arc(r,a,u,V,E),w>0){let at=qe(v,E,r,a);s.arc(at.x,at.y,w,E,b+X)}let Y=qe(P,b,r,a);if(s.lineTo(Y.x,Y.y),k>0){let at=qe(P,gt,r,a);s.arc(at.x,at.y,k,b+X,gt+Math.PI)}let J=(b-k/d+(y+x/d))/2;if(s.arc(r,a,d,b-k/d,J,!0),s.arc(r,a,d,J,y+x/d,!0),x>0){let at=qe(C,nt,r,a);s.arc(at.x,at.y,x,nt+Math.PI,y-X)}let Tt=qe(M,y,r,a);if(s.lineTo(Tt.x,Tt.y),_>0){let at=qe(M,O,r,a);s.arc(at.x,at.y,_,y-X,O)}}else{s.moveTo(r,a);let V=Math.cos(O)*u+r,Y=Math.sin(O)*u+a;s.lineTo(V,Y);let J=Math.cos(E)*u+r,Tt=Math.sin(E)*u+a;s.lineTo(J,Tt)}s.closePath()}function jd(s,t,e,i,n){let{fullCircles:o,startAngle:r,circumference:a}=t,l=t.endAngle;if(o){on(s,t,e,i,l,n);for(let c=0;c<o;++c)s.fill();isNaN(a)||(l=r+(a%j||j))}return on(s,t,e,i,l,n),s.fill(),l}function Ud(s,t,e,i,n){let{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:u,borderDashOffset:d}=l,f=l.borderAlign==="inner";if(!c)return;s.setLineDash(u||[]),s.lineDashOffset=d,f?(s.lineWidth=c*2,s.lineJoin=h||"round"):(s.lineWidth=c,s.lineJoin=h||"bevel");let m=t.endAngle;if(o){on(s,t,e,i,m,n);for(let g=0;g<o;++g)s.stroke();isNaN(a)||(m=r+(a%j||j))}f&&Hd(s,t,m),o||(on(s,t,e,i,m,n),s.stroke())}var ve=class extends yt{constructor(e){super();S(this,"circumference");S(this,"endAngle");S(this,"fullCircles");S(this,"innerRadius");S(this,"outerRadius");S(this,"pixelMargin");S(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,i,n){let o=this.getProps(["x","y"],n),{angle:r,distance:a}=Wn(o,{x:e,y:i}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:u,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),f=(this.options.spacing+this.options.borderWidth)/2,m=I(d,c-l),g=je(r,l,c)&&l!==c,p=m>=j||g,y=Rt(a,h+f,u+f);return p&&y}getCenterPoint(e){let{x:i,y:n,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:c,spacing:h}=this.options,u=(o+r)/2,d=(a+l+h+c)/2;return{x:i+Math.cos(u)*d,y:n+Math.sin(u)*d}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){let{options:i,circumference:n}=this,o=(i.offset||0)/4,r=(i.spacing||0)/2,a=i.circular;if(this.pixelMargin=i.borderAlign==="inner"?.33:0,this.fullCircles=n>j?Math.floor(n/j):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();let l=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(l)*o,Math.sin(l)*o);let c=1-Math.sin(Math.min($,n||0)),h=o*c;e.fillStyle=i.backgroundColor,e.strokeStyle=i.borderColor,jd(e,this,h,r,a),Ud(e,this,h,r,a),e.restore()}};S(ve,"id","arc"),S(ve,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),S(ve,"defaultRoutes",{backgroundColor:"backgroundColor"}),S(ve,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function vl(s,t,e=t){s.lineCap=I(e.borderCapStyle,t.borderCapStyle),s.setLineDash(I(e.borderDash,t.borderDash)),s.lineDashOffset=I(e.borderDashOffset,t.borderDashOffset),s.lineJoin=I(e.borderJoinStyle,t.borderJoinStyle),s.lineWidth=I(e.borderWidth,t.borderWidth),s.strokeStyle=I(e.borderColor,t.borderColor)}function Yd(s,t,e){s.lineTo(e.x,e.y)}function Zd(s){return s.stepped?sa:s.tension||s.cubicInterpolationMode==="monotone"?ia:Yd}function Tl(s,t,e={}){let i=s.length,{start:n=0,end:o=i-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:i,start:l,loop:t.loop,ilen:c<l&&!h?i+c-l:c-l}}function qd(s,t,e,i){let{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=Tl(n,e,i),h=Zd(o),{move:u=!0,reverse:d}=i||{},f,m,g;for(f=0;f<=c;++f)m=n[(a+(d?c-f:f))%r],!m.skip&&(u?(s.moveTo(m.x,m.y),u=!1):h(s,g,m,d,o.stepped),g=m);return l&&(m=n[(a+(d?c:0))%r],h(s,g,m,d,o.stepped)),!!l}function Gd(s,t,e,i){let n=t.points,{count:o,start:r,ilen:a}=Tl(n,e,i),{move:l=!0,reverse:c}=i||{},h=0,u=0,d,f,m,g,p,y,b=w=>(r+(c?a-w:w))%o,_=()=>{g!==p&&(s.lineTo(h,p),s.lineTo(h,g),s.lineTo(h,y))};for(l&&(f=n[b(0)],s.moveTo(f.x,f.y)),d=0;d<=a;++d){if(f=n[b(d)],f.skip)continue;let w=f.x,x=f.y,k=w|0;k===m?(x<g?g=x:x>p&&(p=x),h=(u*h+w)/++u):(_(),s.lineTo(w,x),m=k,u=0,g=p=x),y=x}_()}function Do(s){let t=s.options,e=t.borderDash&&t.borderDash.length;return!s._decimated&&!s._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Gd:qd}function Xd(s){return s.stepped?da:s.tension||s.cubicInterpolationMode==="monotone"?fa:se}function Kd(s,t,e,i){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,i)&&n.closePath()),vl(s,t.options),s.stroke(n)}function Jd(s,t,e,i){let{segments:n,options:o}=t,r=Do(t);for(let a of n)vl(s,o,a.style),s.beginPath(),r(s,t,a,{start:e,end:e+i-1})&&s.closePath(),s.stroke()}var Qd=typeof Path2D=="function";function tf(s,t,e,i){Qd&&!t.options.segment?Kd(s,t,e,i):Jd(s,t,e,i)}var Vt=class extends yt{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){let n=i.spanGaps?this._loop:this._fullLoop;ca(this._points,i,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=ga(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i=this.options,n=t[e],o=this.points,r=oo(this,{property:e,start:n,end:n});if(!r.length)return;let a=[],l=Xd(i),c,h;for(c=0,h=r.length;c<h;++c){let{start:u,end:d}=r[c],f=o[u],m=o[d];if(f===m){a.push(f);continue}let g=Math.abs((n-f[e])/(m[e]-f[e])),p=l(f,m,g,i.stepped);p[e]=t[e],a.push(p)}return a.length===1?a[0]:a}pathSegment(t,e,i){return Do(this)(t,this,e,i)}path(t,e,i){let n=this.segments,o=Do(this),r=this._loop;e=e||0,i=i||this.points.length-e;for(let a of n)r&=o(t,this,a,{start:e,end:e+i-1});return!!r}draw(t,e,i,n){let o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),tf(t,this,i,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}};S(Vt,"id","line"),S(Vt,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),S(Vt,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),S(Vt,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function ja(s,t,e,i){let n=s.options,{[e]:o}=s.getProps([e],i);return Math.abs(t-o)<n.radius+n.hitRadius}var ts=class extends yt{constructor(e){super();S(this,"parsed");S(this,"skip");S(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,i,n){let o=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(e-r,2)+Math.pow(i-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,i){return ja(this,e,"x",i)}inYRange(e,i){return ja(this,e,"y",i)}getCenterPoint(e){let{x:i,y:n}=this.getProps(["x","y"],e);return{x:i,y:n}}size(e){e=e||this.options||{};let i=e.radius||0;i=Math.max(i,i&&e.hoverRadius||0);let n=i&&e.borderWidth||0;return(i+n)*2}draw(e,i){let n=this.options;this.skip||n.radius<.1||!Pt(this,i,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,Hi(e,n,this.x,this.y))}getRange(){let e=this.options||{};return e.radius+e.hitRadius}};S(ts,"id","point"),S(ts,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),S(ts,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Ol(s,t){let{x:e,y:i,base:n,width:o,height:r}=s.getProps(["x","y","base","width","height"],t),a,l,c,h,u;return s.horizontal?(u=r/2,a=Math.min(e,n),l=Math.max(e,n),c=i-u,h=i+u):(u=o/2,a=e-u,l=e+u,c=Math.min(i,n),h=Math.max(i,n)),{left:a,top:c,right:l,bottom:h}}function le(s,t,e,i){return s?0:tt(t,e,i)}function ef(s,t,e){let i=s.options.borderWidth,n=s.borderSkipped,o=Gn(i);return{t:le(n.top,o.top,0,e),r:le(n.right,o.right,0,t),b:le(n.bottom,o.bottom,0,e),l:le(n.left,o.left,0,t)}}function sf(s,t,e){let{enableBorderRadius:i}=s.getProps(["enableBorderRadius"]),n=s.options.borderRadius,o=re(n),r=Math.min(t,e),a=s.borderSkipped,l=i||L(n);return{topLeft:le(!l||a.top||a.left,o.topLeft,0,r),topRight:le(!l||a.top||a.right,o.topRight,0,r),bottomLeft:le(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:le(!l||a.bottom||a.right,o.bottomRight,0,r)}}function nf(s){let t=Ol(s),e=t.right-t.left,i=t.bottom-t.top,n=ef(s,e/2,i/2),o=sf(s,e/2,i/2);return{outer:{x:t.left,y:t.top,w:e,h:i,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:i-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function go(s,t,e,i){let n=t===null,o=e===null,a=s&&!(n&&o)&&Ol(s,i);return a&&(n||Rt(t,a.left,a.right))&&(o||Rt(e,a.top,a.bottom))}function of(s){return s.topLeft||s.topRight||s.bottomLeft||s.bottomRight}function rf(s,t){s.rect(t.x,t.y,t.w,t.h)}function po(s,t,e={}){let i=s.x!==e.x?-t:0,n=s.y!==e.y?-t:0,o=(s.x+s.w!==e.x+e.w?t:0)-i,r=(s.y+s.h!==e.y+e.h?t:0)-n;return{x:s.x+i,y:s.y+n,w:s.w+o,h:s.h+r,radius:s.radius}}var es=class extends yt{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){let{inflateAmount:e,options:{borderColor:i,backgroundColor:n}}=this,{inner:o,outer:r}=nf(this),a=of(r.radius)?Ye:rf;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,po(r,e,o)),t.clip(),a(t,po(o,-e,r)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),a(t,po(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,i){return go(this,t,e,i)}inXRange(t,e){return go(this,t,null,e)}inYRange(t,e){return go(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?i:(i+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}};S(es,"id","bar"),S(es,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),S(es,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var af=Object.freeze({__proto__:null,ArcElement:ve,BarElement:es,LineElement:Vt,PointElement:ts}),Eo=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Ua=Eo.map(s=>s.replace("rgb(","rgba(").replace(")",", 0.5)"));function Dl(s){return Eo[s%Eo.length]}function El(s){return Ua[s%Ua.length]}function lf(s,t){return s.borderColor=Dl(t),s.backgroundColor=El(t),++t}function cf(s,t){return s.backgroundColor=s.data.map(()=>Dl(t++)),t}function hf(s,t){return s.backgroundColor=s.data.map(()=>El(t++)),t}function uf(s){let t=0;return(e,i)=>{let n=s.getDatasetMeta(i).controller;n instanceof Gt?t=cf(e,t):n instanceof Oe?t=hf(e,t):n&&(t=lf(e,t))}}function Ya(s){let t;for(t in s)if(s[t].borderColor||s[t].backgroundColor)return!0;return!1}function df(s){return s&&(s.borderColor||s.backgroundColor)}function ff(){return U.borderColor!=="rgba(0,0,0,0.1)"||U.backgroundColor!=="rgba(0,0,0,0.1)"}var mf={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(s,t,e){if(!e.enabled)return;let{data:{datasets:i},options:n}=s.config,{elements:o}=n,r=Ya(i)||df(n)||o&&Ya(o)||ff();if(!e.forceOverride&&r)return;let a=uf(s);i.forEach(a)}};function gf(s,t,e,i,n){let o=n.samples||i;if(o>=e)return s.slice(t,t+e);let r=[],a=(e-2)/(o-2),l=0,c=t+e-1,h=t,u,d,f,m,g;for(r[l++]=s[h],u=0;u<o-2;u++){let p=0,y=0,b,_=Math.floor((u+1)*a)+1+t,w=Math.min(Math.floor((u+2)*a)+1,e)+t,x=w-_;for(b=_;b<w;b++)p+=s[b].x,y+=s[b].y;p/=x,y/=x;let k=Math.floor(u*a)+1+t,M=Math.min(Math.floor((u+1)*a)+1,e)+t,{x:v,y:O}=s[h];for(f=m=-1,b=k;b<M;b++)m=.5*Math.abs((v-p)*(s[b].y-O)-(v-s[b].x)*(y-O)),m>f&&(f=m,d=s[b],g=b);r[l++]=d,h=g}return r[l++]=s[c],r}function pf(s,t,e,i){let n=0,o=0,r,a,l,c,h,u,d,f,m,g,p=[],y=t+e-1,b=s[t].x,w=s[y].x-b;for(r=t;r<t+e;++r){a=s[r],l=(a.x-b)/w*i,c=a.y;let x=l|0;if(x===h)c<m?(m=c,u=r):c>g&&(g=c,d=r),n=(o*n+a.x)/++o;else{let k=r-1;if(!A(u)&&!A(d)){let M=Math.min(u,d),v=Math.max(u,d);M!==f&&M!==k&&p.push({...s[M],x:n}),v!==f&&v!==k&&p.push({...s[v],x:n})}r>0&&k!==f&&p.push(s[k]),p.push(a),h=x,o=0,m=g=c,u=d=f=r}}return p}function Il(s){if(s._decimated){let t=s._data;delete s._decimated,delete s._data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function Za(s){s.data.datasets.forEach(t=>{Il(t)})}function yf(s,t){let e=t.length,i=0,n,{iScale:o}=s,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(i=tt(Lt(t,o.axis,r).lo,0,e-1)),c?n=tt(Lt(t,o.axis,a).hi+1,i,e)-i:n=e-i,{start:i,count:n}}var bf={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(s,t,e)=>{if(!e.enabled){Za(s);return}let i=s.width;s.data.datasets.forEach((n,o)=>{let{_data:r,indexAxis:a}=n,l=s.getDatasetMeta(o),c=r||n.data;if(Ze([a,s.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;let h=s.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||s.options.parsing)return;let{start:u,count:d}=yf(l,c),f=e.threshold||4*i;if(d<=f){Il(n);return}A(r)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(g){this._data=g}}));let m;switch(e.algorithm){case"lttb":m=gf(c,u,d,i,e);break;case"min-max":m=pf(c,u,d,i);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}n._decimated=m})},destroy(s){Za(s)}};function xf(s,t,e){let i=s.segments,n=s.points,o=t.points,r=[];for(let a of i){let{start:l,end:c}=a;c=Ao(l,c,n);let h=Io(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}let u=oo(t,h);for(let d of u){let f=Io(e,o[d.start],o[d.end],d.loop),m=no(a,n,f);for(let g of m)r.push({source:g,target:d,start:{[e]:qa(h,f,"start",Math.max)},end:{[e]:qa(h,f,"end",Math.min)}})}}return r}function Io(s,t,e,i){if(i)return;let n=t[s],o=e[s];return s==="angle"&&(n=ht(n),o=ht(o)),{property:s,start:n,end:o}}function _f(s,t){let{x:e=null,y:i=null}=s||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=Ao(r,a,n);let l=n[r],c=n[a];i!==null?(o.push({x:l.x,y:i}),o.push({x:c.x,y:i})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function Ao(s,t,e){for(;t>s;t--){let i=e[t];if(!isNaN(i.x)&&!isNaN(i.y))break}return t}function qa(s,t,e,i){return s&&t?i(s[e],t[e]):s?s[e]:t?t[e]:0}function Cl(s,t){let e=[],i=!1;return B(s)?(i=!0,e=s):e=_f(s,t),e.length?new Vt({points:e,options:{tension:0},_loop:i,_fullLoop:i}):null}function Ga(s){return s&&s.fill!==!1}function wf(s,t,e){let n=s[t].fill,o=[t],r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!Z(n))return n;if(r=s[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function Sf(s,t,e){let i=Tf(s);if(L(i))return isNaN(i.value)?!1:i;let n=parseFloat(i);return Z(n)&&Math.floor(n)===n?kf(i[0],t,n,e):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function kf(s,t,e,i){return(s==="-"||s==="+")&&(e=t+e),e===t||e<0||e>=i?!1:e}function Mf(s,t){let e=null;return s==="start"?e=t.bottom:s==="end"?e=t.top:L(s)?e=t.getPixelForValue(s.value):t.getBasePixel&&(e=t.getBasePixel()),e}function vf(s,t,e){let i;return s==="start"?i=e:s==="end"?i=t.options.reverse?t.min:t.max:L(s)?i=s.value:i=t.getBaseValue(),i}function Tf(s){let t=s.options,e=t.fill,i=I(e&&e.target,e);return i===void 0&&(i=!!t.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function Of(s){let{scale:t,index:e,line:i}=s,n=[],o=i.segments,r=i.points,a=Df(t,e);a.push(Cl({x:null,y:t.bottom},i));for(let l=0;l<o.length;l++){let c=o[l];for(let h=c.start;h<=c.end;h++)Ef(n,r[h],a)}return new Vt({points:n,options:{}})}function Df(s,t){let e=[],i=s.getMatchingVisibleMetas("line");for(let n=0;n<i.length;n++){let o=i[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function Ef(s,t,e){let i=[];for(let n=0;n<e.length;n++){let o=e[n],{first:r,last:a,point:l}=If(o,t,"x");if(!(!l||r&&a)){if(r)i.unshift(l);else if(s.push(l),!a)break}}s.push(...i)}function If(s,t,e){let i=s.interpolate(t,e);if(!i)return{};let n=i[e],o=s.segments,r=s.points,a=!1,l=!1;for(let c=0;c<o.length;c++){let h=o[c],u=r[h.start][e],d=r[h.end][e];if(Rt(n,u,d)){a=n===u,l=n===d;break}}return{first:a,last:l,point:i}}var rn=class{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:n,y:o,radius:r}=this;return e=e||{start:0,end:j},t.arc(n,o,r,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:i+Math.sin(o)*n,angle:o}}};function Cf(s){let{chart:t,fill:e,line:i}=s;if(Z(e))return Ff(t,e);if(e==="stack")return Of(s);if(e==="shape")return!0;let n=Af(s);return n instanceof rn?n:Cl(n,i)}function Ff(s,t){let e=s.getDatasetMeta(t);return e&&s.isDatasetVisible(t)?e.dataset:null}function Af(s){return(s.scale||{}).getPointPositionForValue?Pf(s):Lf(s)}function Lf(s){let{scale:t={},fill:e}=s,i=Mf(e,t);if(Z(i)){let n=t.isHorizontal();return{x:n?i:null,y:n?null:i}}return null}function Pf(s){let{scale:t,fill:e}=s,i=t.options,n=t.getLabels().length,o=i.reverse?t.max:t.min,r=vf(e,t,o),a=[];if(i.grid.circular){let l=t.getPointPositionForValue(0,o);return new rn({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function yo(s,t,e){let i=Cf(t),{chart:n,index:o,line:r,scale:a,axis:l}=t,c=r.options,h=c.fill,u=c.backgroundColor,{above:d=u,below:f=u}=h||{},m=n.getDatasetMeta(o),g=ro(n,m);i&&r.points.length&&(Ds(s,e),Nf(s,{line:r,target:i,above:d,below:f,area:e,scale:a,axis:l,clip:g}),Es(s))}function Nf(s,t){let{line:e,target:i,above:n,below:o,area:r,scale:a,clip:l}=t,c=e._loop?"angle":t.axis;s.save(),c==="x"&&o!==n&&(Xa(s,i,r.top),Ka(s,{line:e,target:i,color:n,scale:a,property:c,clip:l}),s.restore(),s.save(),Xa(s,i,r.bottom)),Ka(s,{line:e,target:i,color:o,scale:a,property:c,clip:l}),s.restore()}function Xa(s,t,e){let{segments:i,points:n}=t,o=!0,r=!1;s.beginPath();for(let a of i){let{start:l,end:c}=a,h=n[l],u=n[Ao(l,c,n)];o?(s.moveTo(h.x,h.y),o=!1):(s.lineTo(h.x,e),s.lineTo(h.x,h.y)),r=!!t.pathSegment(s,a,{move:r}),r?s.closePath():s.lineTo(u.x,e)}s.lineTo(t.first().x,e),s.closePath(),s.clip()}function Ka(s,t){let{line:e,target:i,property:n,color:o,scale:r,clip:a}=t,l=xf(e,i,n);for(let{source:c,target:h,start:u,end:d}of l){let{style:{backgroundColor:f=o}={}}=c,m=i!==!0;s.save(),s.fillStyle=f,Rf(s,r,a,m&&Io(n,u,d)),s.beginPath();let g=!!e.pathSegment(s,c),p;if(m){g?s.closePath():Ja(s,i,d,n);let y=!!i.pathSegment(s,h,{move:g,reverse:!0});p=g&&y,p||Ja(s,i,u,n)}s.closePath(),s.fill(p?"evenodd":"nonzero"),s.restore()}}function Rf(s,t,e,i){let n=t.chart.chartArea,{property:o,start:r,end:a}=i||{};if(o==="x"||o==="y"){let l,c,h,u;o==="x"?(l=r,c=n.top,h=a,u=n.bottom):(l=n.left,c=r,h=n.right,u=a),s.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),u=Math.min(u,e.bottom)),s.rect(l,c,h-l,u-c),s.clip()}}function Ja(s,t,e,i){let n=t.interpolate(e,i);n&&s.lineTo(n.x,n.y)}var Wf={id:"filler",afterDatasetsUpdate(s,t,e){let i=(s.data.datasets||[]).length,n=[],o,r,a,l;for(r=0;r<i;++r)o=s.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof Vt&&(l={visible:s.isDatasetVisible(r),index:r,fill:Sf(a,r,i),chart:s,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<i;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=wf(n,r,e.propagate))},beforeDraw(s,t,e){let i=e.drawTime==="beforeDraw",n=s.getSortedVisibleDatasetMetas(),o=s.chartArea;for(let r=n.length-1;r>=0;--r){let a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),i&&a.fill&&yo(s.ctx,a,o))}},beforeDatasetsDraw(s,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;let i=s.getSortedVisibleDatasetMetas();for(let n=i.length-1;n>=0;--n){let o=i[n].$filler;Ga(o)&&yo(s.ctx,o,s.chartArea)}},beforeDatasetDraw(s,t,e){let i=t.meta.$filler;!Ga(i)||e.drawTime!=="beforeDatasetDraw"||yo(s.ctx,i,s.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}},Qa=(s,t)=>{let{boxHeight:e=t,boxWidth:i=t}=s;return s.usePointStyle&&(e=Math.min(e,t),i=s.pointStyleWidth||Math.min(i,t)),{boxWidth:i,boxHeight:e,itemHeight:Math.max(t,e)}},zf=(s,t)=>s!==null&&t!==null&&s.datasetIndex===t.datasetIndex&&s.index===t.index,an=class extends yt{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=H(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(i=>t.filter(i,this.chart.data))),t.sort&&(e=e.sort((i,n)=>t.sort(i,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}let i=t.labels,n=Q(i.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Qa(i,o),c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,n){let{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a,u=t;o.textAlign="left",o.textBaseline="middle";let d=-1,f=-h;return this.legendItems.forEach((m,g)=>{let p=i+e/2+o.measureText(m.text).width;(g===0||c[c.length-1]+p+2*a>r)&&(u+=h,c[c.length-(g>0?0:1)]=0,f+=h,d++),l[g]={left:0,top:f,row:d,width:p,height:n},c[c.length-1]+=p+a}),u}_fitCols(t,e,i,n){let{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t,u=a,d=0,f=0,m=0,g=0;return this.legendItems.forEach((p,y)=>{let{itemWidth:b,itemHeight:_}=Vf(i,e,o,p,n);y>0&&f+_+2*a>h&&(u+=d+a,c.push({width:d,height:f}),m+=d+a,g++,d=f=0),l[y]={left:m,top:f,col:g,width:b,height:_},d=Math.max(d,b),f+=_+a}),u+=d,c.push({width:d,height:f}),u}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:n},rtl:o}}=this,r=Se(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=ot(i,this.left+n,this.right-this.lineWidths[a]);for(let c of e)a!==c.row&&(a=c.row,l=ot(i,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=ot(i,this.top+t+n,this.bottom-this.columnSizes[a].height);for(let c of e)c.col!==a&&(a=c.col,l=ot(i,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){let t=this.ctx;Ds(t,this),this._draw(),Es(t)}}_draw(){let{options:t,columnSizes:e,lineWidths:i,ctx:n}=this,{align:o,labels:r}=t,a=U.color,l=Se(t.rtl,this.left,this.width),c=Q(r.font),{padding:h}=r,u=c.size,d=u/2,f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;let{boxWidth:m,boxHeight:g,itemHeight:p}=Qa(r,u),y=function(k,M,v){if(isNaN(m)||m<=0||isNaN(g)||g<0)return;n.save();let O=I(v.lineWidth,1);if(n.fillStyle=I(v.fillStyle,a),n.lineCap=I(v.lineCap,"butt"),n.lineDashOffset=I(v.lineDashOffset,0),n.lineJoin=I(v.lineJoin,"miter"),n.lineWidth=O,n.strokeStyle=I(v.strokeStyle,a),n.setLineDash(I(v.lineDash,[])),r.usePointStyle){let E={radius:g*Math.SQRT2/2,pointStyle:v.pointStyle,rotation:v.rotation,borderWidth:O},C=l.xPlus(k,m/2),P=M+d;qn(n,E,C,P,r.pointStyleWidth&&m)}else{let E=M+Math.max((u-g)/2,0),C=l.leftForLtr(k,m),P=re(v.borderRadius);n.beginPath(),Object.values(P).some(nt=>nt!==0)?Ye(n,{x:C,y:E,w:m,h:g,radius:P}):n.rect(C,E,m,g),n.fill(),O!==0&&n.stroke()}n.restore()},b=function(k,M,v){oe(n,v.text,k,M+p/2,c,{strikethrough:v.hidden,textAlign:l.textAlign(v.textAlign)})},_=this.isHorizontal(),w=this._computeTitleHeight();_?f={x:ot(o,this.left+h,this.right-i[0]),y:this.top+h+w,line:0}:f={x:this.left+h,y:ot(o,this.top+w+h,this.bottom-e[0].height),line:0},so(this.ctx,t.textDirection);let x=p+h;this.legendItems.forEach((k,M)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;let v=n.measureText(k.text).width,O=l.textAlign(k.textAlign||(k.textAlign=r.textAlign)),E=m+d+v,C=f.x,P=f.y;l.setWidth(this.width),_?M>0&&C+E+h>this.right&&(P=f.y+=x,f.line++,C=f.x=ot(o,this.left+h,this.right-i[f.line])):M>0&&P+x>this.bottom&&(C=f.x=C+e[f.line].width+h,f.line++,P=f.y=ot(o,this.top+w+h,this.bottom-e[f.line].height));let nt=l.x(C);if(y(nt,P,k),C=Qr(O,C+m+d,_?C+E:this.right,t.rtl),b(l.x(C),P,k),_)f.x+=E+h;else if(typeof k.text!="string"){let gt=c.lineHeight;f.y+=Fl(k,gt)+h}else f.y+=x}),io(this.ctx,t.textDirection)}drawTitle(){let t=this.options,e=t.title,i=Q(e.font),n=rt(e.padding);if(!e.display)return;let o=Se(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=i.size/2,c=n.top+l,h,u=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),h=this.top+c,u=ot(t.align,u,this.right-d);else{let m=this.columnSizes.reduce((g,p)=>Math.max(g,p.height),0);h=c+ot(t.align,this.top,this.bottom-m-t.labels.padding-this._computeTitleHeight())}let f=ot(a,u,u+d);r.textAlign=o.textAlign(zi(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=i.string,oe(r,e.text,f,h,i)}_computeTitleHeight(){let t=this.options.title,e=Q(t.font),i=rt(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,n,o;if(Rt(t,this.left,this.right)&&Rt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,i=0;i<o.length;++i)if(n=o[i],Rt(t,n.left,n.left+n.width)&&Rt(e,n.top,n.top+n.height))return this.legendItems[i]}return null}handleEvent(t){let e=this.options;if(!$f(t.type,e))return;let i=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){let n=this._hoveredItem,o=zf(n,i);n&&!o&&H(e.onLeave,[t,n,this],this),this._hoveredItem=i,i&&!o&&H(e.onHover,[t,i,this],this)}else i&&H(e.onClick,[t,i,this],this)}};function Vf(s,t,e,i,n){let o=Hf(i,s,t,e),r=Bf(n,i,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Hf(s,t,e,i){let n=s.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+i.measureText(n).width}function Bf(s,t,e){let i=s;return typeof t.text!="string"&&(i=Fl(t,e)),i}function Fl(s,t){let e=s.text?s.text.length:0;return t*e}function $f(s,t){return!!((s==="mousemove"||s==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(s==="click"||s==="mouseup"))}var jf={id:"legend",_element:an,start(s,t,e){let i=s.legend=new an({ctx:s.ctx,options:e,chart:s});lt.configure(s,i,e),lt.addBox(s,i)},stop(s){lt.removeBox(s,s.legend),delete s.legend},beforeUpdate(s,t,e){let i=s.legend;lt.configure(s,i,e),i.options=e},afterUpdate(s){let t=s.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(s,t){t.replay||s.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(s,t,e){let i=t.datasetIndex,n=e.chart;n.isDatasetVisible(i)?(n.hide(i),t.hidden=!0):(n.show(i),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:s=>s.chart.options.color,boxWidth:40,padding:10,generateLabels(s){let t=s.data.datasets,{labels:{usePointStyle:e,pointStyle:i,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=s.legend.options;return s._getSortedDatasetMetas().map(l=>{let c=l.controller.getStyle(e?0:void 0),h=rt(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:i||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:s=>s.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:s=>!s.startsWith("on"),labels:{_scriptable:s=>!["generateLabels","filter","sort"].includes(s)}}},js=class extends yt{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let n=B(i.text)?i.text.length:1;this._padding=rt(i.padding);let o=n*Q(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){let t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){let{top:e,left:i,bottom:n,right:o,options:r}=this,a=r.align,l=0,c,h,u;return this.isHorizontal()?(h=ot(a,i,o),u=e+t,c=o-i):(r.position==="left"?(h=i+t,u=ot(a,n,e),l=$*-.5):(h=o-t,u=ot(a,e,n),l=$*.5),c=n-e),{titleX:h,titleY:u,maxWidth:c,rotation:l}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=Q(e.font),o=i.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);oe(t,e.text,0,0,i,{color:e.color,maxWidth:l,rotation:c,textAlign:zi(e.align),textBaseline:"middle",translation:[r,a]})}};function Uf(s,t){let e=new js({ctx:s.ctx,options:t,chart:s});lt.configure(s,e,t),lt.addBox(s,e),s.titleBlock=e}var Yf={id:"title",_element:js,start(s,t,e){Uf(s,e)},stop(s){let t=s.titleBlock;lt.removeBox(s,t),delete s.titleBlock},beforeUpdate(s,t,e){let i=s.titleBlock;lt.configure(s,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Xi=new WeakMap,Zf={id:"subtitle",start(s,t,e){let i=new js({ctx:s.ctx,options:e,chart:s});lt.configure(s,i,e),lt.addBox(s,i),Xi.set(s,i)},stop(s){lt.removeBox(s,Xi.get(s)),Xi.delete(s)},beforeUpdate(s,t,e){let i=Xi.get(s);lt.configure(s,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Rs={average(s){if(!s.length)return!1;let t,e,i=new Set,n=0,o=0;for(t=0,e=s.length;t<e;++t){let a=s[t].element;if(a&&a.hasValue()){let l=a.tooltipPosition();i.add(l.x),n+=l.y,++o}}return o===0||i.size===0?!1:{x:[...i].reduce((a,l)=>a+l)/i.size,y:n/o}},nearest(s,t){if(!s.length)return!1;let e=t.x,i=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=s.length;o<r;++o){let l=s[o].element;if(l&&l.hasValue()){let c=l.getCenterPoint(),h=Li(t,c);h<n&&(n=h,a=l)}}if(a){let l=a.tooltipPosition();e=l.x,i=l.y}return{x:e,y:i}}};function Wt(s,t){return t&&(B(t)?Array.prototype.push.apply(s,t):s.push(t)),s}function qt(s){return(typeof s=="string"||s instanceof String)&&s.indexOf(`
`)>-1?s.split(`
`):s}function qf(s,t){let{element:e,datasetIndex:i,index:n}=t,o=s.getDatasetMeta(i).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:s,label:r,parsed:o.getParsed(n),raw:s.data.datasets[i].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:i,element:e}}function tl(s,t){let e=s.chart.ctx,{body:i,footer:n,title:o}=s,{boxWidth:r,boxHeight:a}=t,l=Q(t.bodyFont),c=Q(t.titleFont),h=Q(t.footerFont),u=o.length,d=n.length,f=i.length,m=rt(t.padding),g=m.height,p=0,y=i.reduce((w,x)=>w+x.before.length+x.lines.length+x.after.length,0);if(y+=s.beforeBody.length+s.afterBody.length,u&&(g+=u*c.lineHeight+(u-1)*t.titleSpacing+t.titleMarginBottom),y){let w=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=f*w+(y-f)*l.lineHeight+(y-1)*t.bodySpacing}d&&(g+=t.footerMarginTop+d*h.lineHeight+(d-1)*t.footerSpacing);let b=0,_=function(w){p=Math.max(p,e.measureText(w).width+b)};return e.save(),e.font=c.string,z(s.title,_),e.font=l.string,z(s.beforeBody.concat(s.afterBody),_),b=t.displayColors?r+2+t.boxPadding:0,z(i,w=>{z(w.before,_),z(w.lines,_),z(w.after,_)}),b=0,e.font=h.string,z(s.footer,_),e.restore(),p+=m.width,{width:p,height:g}}function Gf(s,t){let{y:e,height:i}=t;return e<i/2?"top":e>s.height-i/2?"bottom":"center"}function Xf(s,t,e,i){let{x:n,width:o}=i,r=e.caretSize+e.caretPadding;if(s==="left"&&n+o+r>t.width||s==="right"&&n-o-r<0)return!0}function Kf(s,t,e,i){let{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=s,c="center";return i==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Xf(c,s,t,e)&&(c="center"),c}function el(s,t,e){let i=e.yAlign||t.yAlign||Gf(s,e);return{xAlign:e.xAlign||t.xAlign||Kf(s,t,e,i),yAlign:i}}function Jf(s,t){let{x:e,width:i}=s;return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function Qf(s,t,e){let{y:i,height:n}=s;return t==="top"?i+=e:t==="bottom"?i-=n+e:i-=n/2,i}function sl(s,t,e,i){let{caretSize:n,caretPadding:o,cornerRadius:r}=s,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:u,bottomLeft:d,bottomRight:f}=re(r),m=Jf(t,a),g=Qf(t,l,c);return l==="center"?a==="left"?m+=c:a==="right"&&(m-=c):a==="left"?m-=Math.max(h,d)+n:a==="right"&&(m+=Math.max(u,f)+n),{x:tt(m,0,i.width-t.width),y:tt(g,0,i.height-t.height)}}function Ki(s,t,e){let i=rt(e.padding);return t==="center"?s.x+s.width/2:t==="right"?s.x+s.width-i.right:s.x+i.left}function il(s){return Wt([],qt(s))}function tm(s,t,e){return Yt(s,{tooltip:t,tooltipItems:e,type:"tooltip"})}function nl(s,t){let e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?s.override(e):s}var Al={beforeTitle:Nt,title(s){if(s.length>0){let t=s[0],e=t.chart.data.labels,i=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return e[t.dataIndex]}return""},afterTitle:Nt,beforeBody:Nt,beforeLabel:Nt,label(s){if(this&&this.options&&this.options.mode==="dataset")return s.label+": "+s.formattedValue||s.formattedValue;let t=s.dataset.label||"";t&&(t+=": ");let e=s.formattedValue;return A(e)||(t+=e),t},labelColor(s){let e=s.chart.getDatasetMeta(s.datasetIndex).controller.getStyle(s.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(s){let e=s.chart.getDatasetMeta(s.datasetIndex).controller.getStyle(s.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Nt,afterBody:Nt,beforeFooter:Nt,footer:Nt,afterFooter:Nt};function dt(s,t,e,i){let n=s[t].call(e,i);return typeof n>"u"?Al[t].call(e,i):n}var Bs=class extends yt{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),n=i.enabled&&e.options.animation&&i.animations,o=new en(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=tm(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){let{callbacks:i}=e,n=dt(i,"beforeTitle",this,t),o=dt(i,"title",this,t),r=dt(i,"afterTitle",this,t),a=[];return a=Wt(a,qt(n)),a=Wt(a,qt(o)),a=Wt(a,qt(r)),a}getBeforeBody(t,e){return il(dt(e.callbacks,"beforeBody",this,t))}getBody(t,e){let{callbacks:i}=e,n=[];return z(t,o=>{let r={before:[],lines:[],after:[]},a=nl(i,o);Wt(r.before,qt(dt(a,"beforeLabel",this,o))),Wt(r.lines,dt(a,"label",this,o)),Wt(r.after,qt(dt(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return il(dt(e.callbacks,"afterBody",this,t))}getFooter(t,e){let{callbacks:i}=e,n=dt(i,"beforeFooter",this,t),o=dt(i,"footer",this,t),r=dt(i,"afterFooter",this,t),a=[];return a=Wt(a,qt(n)),a=Wt(a,qt(o)),a=Wt(a,qt(r)),a}_createItems(t){let e=this._active,i=this.chart.data,n=[],o=[],r=[],a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(qf(this.chart,e[l]));return t.filter&&(a=a.filter((h,u,d)=>t.filter(h,u,d,i))),t.itemSort&&(a=a.sort((h,u)=>t.itemSort(h,u,i))),z(a,h=>{let u=nl(t.callbacks,h);n.push(dt(u,"labelColor",this,h)),o.push(dt(u,"labelPointStyle",this,h)),r.push(dt(u,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){let i=this.options.setContext(this.getContext()),n=this._active,o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{let a=Rs[i.position].call(this,n,this._eventPosition);r=this._createItems(i),this.title=this.getTitle(r,i),this.beforeBody=this.getBeforeBody(r,i),this.body=this.getBody(r,i),this.afterBody=this.getAfterBody(r,i),this.footer=this.getFooter(r,i);let l=this._size=tl(this,i),c=Object.assign({},a,l),h=el(this.chart,i,c),u=sl(i,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:u.x,y:u.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,n){let o=this.getCaretPosition(t,i,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,i){let{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:u}=re(a),{x:d,y:f}=t,{width:m,height:g}=e,p,y,b,_,w,x;return o==="center"?(w=f+g/2,n==="left"?(p=d,y=p-r,_=w+r,x=w-r):(p=d+m,y=p+r,_=w-r,x=w+r),b=p):(n==="left"?y=d+Math.max(l,h)+r:n==="right"?y=d+m-Math.max(c,u)-r:y=this.caretX,o==="top"?(_=f,w=_-r,p=y-r,b=y+r):(_=f+g,w=_+r,p=y+r,b=y-r),x=_),{x1:p,x2:y,x3:b,y1:_,y2:w,y3:x}}drawTitle(t,e,i){let n=this.title,o=n.length,r,a,l;if(o){let c=Se(i.rtl,this.x,this.width);for(t.x=Ki(this,i.titleAlign,i),e.textAlign=c.textAlign(i.titleAlign),e.textBaseline="middle",r=Q(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,n,o){let r=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c}=o,h=Q(o.bodyFont),u=Ki(this,"left",o),d=n.x(u),f=l<h.lineHeight?(h.lineHeight-l)/2:0,m=e.y+f;if(o.usePointStyle){let g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},p=n.leftForLtr(d,c)+c/2,y=m+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,Hi(t,g,p,y),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,Hi(t,g,p,y)}else{t.lineWidth=L(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;let g=n.leftForLtr(d,c),p=n.leftForLtr(n.xPlus(d,1),c-2),y=re(r.borderRadius);Object.values(y).some(b=>b!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Ye(t,{x:g,y:m,w:c,h:l,radius:y}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Ye(t,{x:p,y:m+1,w:c-2,h:l-2,radius:y}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(g,m,c,l),t.strokeRect(g,m,c,l),t.fillStyle=r.backgroundColor,t.fillRect(p,m+1,c-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=i,u=Q(i.bodyFont),d=u.lineHeight,f=0,m=Se(i.rtl,this.x,this.width),g=function(v){e.fillText(v,m.x(t.x+f),t.y+d/2),t.y+=d+o},p=m.textAlign(r),y,b,_,w,x,k,M;for(e.textAlign=r,e.textBaseline="middle",e.font=u.string,t.x=Ki(this,p,i),e.fillStyle=i.bodyColor,z(this.beforeBody,g),f=a&&p!=="right"?r==="center"?c/2+h:c+2+h:0,w=0,k=n.length;w<k;++w){for(y=n[w],b=this.labelTextColors[w],e.fillStyle=b,z(y.before,g),_=y.lines,a&&_.length&&(this._drawColorBox(e,t,w,m,i),d=Math.max(u.lineHeight,l)),x=0,M=_.length;x<M;++x)g(_[x]),d=u.lineHeight;z(y.after,g)}f=0,d=u.lineHeight,z(this.afterBody,g),t.y-=o}drawFooter(t,e,i){let n=this.footer,o=n.length,r,a;if(o){let l=Se(i.rtl,this.x,this.width);for(t.x=Ki(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=l.textAlign(i.footerAlign),e.textBaseline="middle",r=Q(i.footerFont),e.fillStyle=i.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+i.footerSpacing}}drawBackground(t,e,i,n){let{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=i,{topLeft:u,topRight:d,bottomLeft:f,bottomRight:m}=re(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+u,l),r==="top"&&this.drawCaret(t,e,i,n),e.lineTo(a+c-d,l),e.quadraticCurveTo(a+c,l,a+c,l+d),r==="center"&&o==="right"&&this.drawCaret(t,e,i,n),e.lineTo(a+c,l+h-m),e.quadraticCurveTo(a+c,l+h,a+c-m,l+h),r==="bottom"&&this.drawCaret(t,e,i,n),e.lineTo(a+f,l+h),e.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,e,i,n),e.lineTo(a,l+u),e.quadraticCurveTo(a,l,a+u,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,n=i&&i.x,o=i&&i.y;if(n||o){let r=Rs[t.position].call(this,this._active,this._eventPosition);if(!r)return;let a=this._size=tl(this,t),l=Object.assign({},r,this._size),c=el(e,t,l),h=sl(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let n={width:this.width,height:this.height},o={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;let r=rt(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(o,t,n,e),so(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),io(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,n=t.map(({datasetIndex:a,index:l})=>{let c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!Ts(i,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,i),a=this._positionChanged(r,t),l=e||!Ts(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,i,n){let o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);let r=this.chart.getElementsAtEventForMode(t,o.mode,o,i);return o.reverse&&r.reverse(),r}_positionChanged(t,e){let{caretX:i,caretY:n,options:o}=this,r=Rs[o.position].call(this,t,e);return r!==!1&&(i!==r.x||n!==r.y)}};S(Bs,"positioners",Rs);var em={id:"tooltip",_element:Bs,positioners:Rs,afterInit(s,t,e){e&&(s.tooltip=new Bs({chart:s,options:e}))},beforeUpdate(s,t,e){s.tooltip&&s.tooltip.initialize(e)},reset(s,t,e){s.tooltip&&s.tooltip.initialize(e)},afterDraw(s){let t=s.tooltip;if(t&&t._willRender()){let e={tooltip:t};if(s.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(s.ctx),s.notifyPlugins("afterTooltipDraw",e)}},afterEvent(s,t){if(s.tooltip){let e=t.replay;s.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(s,t)=>t.bodyFont.size,boxWidth:(s,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Al},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:s=>s!=="filter"&&s!=="itemSort"&&s!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},sm=Object.freeze({__proto__:null,Colors:mf,Decimation:bf,Filler:Wf,Legend:jf,SubTitle:Zf,Title:Yf,Tooltip:em}),im=(s,t,e,i)=>(typeof t=="string"?(e=s.push(t)-1,i.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function nm(s,t,e,i){let n=s.indexOf(t);if(n===-1)return im(s,t,e,i);let o=s.lastIndexOf(t);return n!==o?e:n}var om=(s,t)=>s===null?null:tt(Math.round(s),0,t);function ol(s){let t=this.getLabels();return s>=0&&s<t.length?t[s]:s}var Ws=class extends Ee{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let i=this.getLabels();for(let{index:n,label:o}of e)i[n]===o&&i.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(A(t))return null;let i=this.getLabels();return e=isFinite(e)&&i[e]===t?e:nm(i,t,I(e,t),this._addedLabels),om(e,i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),e||(n=this.getLabels().length-1)),this.min=i,this.max=n}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,n=[],o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return ol.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}};S(Ws,"id","category"),S(Ws,"defaults",{ticks:{callback:ol}});function rm(s,t){let e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:u,includeBounds:d}=s,f=o||1,m=h-1,{min:g,max:p}=t,y=!A(r),b=!A(a),_=!A(c),w=(p-g)/(u+1),x=Pn((p-g)/m/f)*f,k,M,v,O;if(x<1e-14&&!y&&!b)return[{value:g},{value:p}];O=Math.ceil(p/x)-Math.floor(g/x),O>m&&(x=Pn(O*x/m/f)*f),A(l)||(k=Math.pow(10,l),x=Math.ceil(x*k)/k),n==="ticks"?(M=Math.floor(g/x)*x,v=Math.ceil(p/x)*x):(M=g,v=p),y&&b&&o&&Yr((a-r)/o,x/1e3)?(O=Math.round(Math.min((a-r)/x,h)),x=(a-r)/O,M=r,v=a):_?(M=y?r:M,v=b?a:v,O=c-1,x=(v-M)/O):(O=(v-M)/x,$e(O,Math.round(O),x/1e3)?O=Math.round(O):O=Math.ceil(O));let E=Math.max(Rn(x),Rn(M));k=Math.pow(10,A(l)?E:l),M=Math.round(M*k)/k,v=Math.round(v*k)/k;let C=0;for(y&&(d&&M!==r?(e.push({value:r}),M<r&&C++,$e(Math.round((M+C*x)*k)/k,r,rl(r,w,s))&&C++):M<r&&C++);C<O;++C){let P=Math.round((M+C*x)*k)/k;if(b&&P>a)break;e.push({value:P})}return b&&d&&v!==a?e.length&&$e(e[e.length-1].value,a,rl(a,w,s))?e[e.length-1].value=a:e.push({value:a}):(!b||v===a)&&e.push({value:v}),e}function rl(s,t,{horizontal:e,minRotation:i}){let n=wt(i),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+s).length;return Math.min(t/o,r)}var is=class extends Ee{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return A(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:n,max:o}=this,r=l=>n=e?n:l,a=l=>o=i?o:l;if(t){let l=Ot(n),c=Ot(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){let t=this.options.ticks,{maxTicksLimit:e,stepSize:i}=t,n;return i?(n=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit();i=Math.max(2,i);let n={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=rm(n,o);return t.bounds==="ticks"&&Nn(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let n=(i-e)/Math.max(t.length-1,1)/2;e-=n,i+=n}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return Ue(t,this.chart.options.locale,this.options.ticks.format)}},zs=class extends is{determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=Z(t)?t:0,this.max=Z(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=wt(this.options.ticks.minRotation),n=(t?Math.sin(i):Math.cos(i))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}};S(zs,"id","linear"),S(zs,"defaults",{ticks:{callback:Os.formatters.numeric}});var Us=s=>Math.floor(jt(s)),Me=(s,t)=>Math.pow(10,Us(s)+t);function al(s){return s/Math.pow(10,Us(s))===1}function ll(s,t,e){let i=Math.pow(10,e),n=Math.floor(s/i);return Math.ceil(t/i)-n}function am(s,t){let e=t-s,i=Us(e);for(;ll(s,t,i)>10;)i++;for(;ll(s,t,i)<10;)i--;return Math.min(i,Us(s))}function lm(s,{min:t,max:e}){t=ut(s.min,t);let i=[],n=Us(t),o=am(t,e),r=o<0?Math.pow(10,Math.abs(o)):1,a=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*r)/r,h=Math.floor((t-l)/a/10)*a*10,u=Math.floor((c-h)/Math.pow(10,o)),d=ut(s.min,Math.round((l+h+u*Math.pow(10,o))*r)/r);for(;d<e;)i.push({value:d,major:al(d),significand:u}),u>=10?u=u<15?15:20:u++,u>=20&&(o++,u=2,r=o>=0?1:r),d=Math.round((l+h+u*Math.pow(10,o))*r)/r;let f=ut(s.max,d);return i.push({value:f,major:al(f),significand:u}),i}var Vs=class extends Ee{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=is.prototype.parse.apply(this,[t,e]);if(i===0){this._zero=!0;return}return Z(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=Z(t)?Math.max(0,t):null,this.max=Z(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Z(this._userMin)&&(this.min=t===Me(this.min,0)?Me(this.min,-1):Me(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,n=this.max,o=a=>i=t?i:a,r=a=>n=e?n:a;i===n&&(i<=0?(o(1),r(10)):(o(Me(i,-1)),r(Me(n,1)))),i<=0&&o(Me(n,-1)),n<=0&&r(Me(i,1)),this.min=i,this.max=n}buildTicks(){let t=this.options,e={min:this._userMin,max:this._userMax},i=lm(e,this);return t.bounds==="ticks"&&Nn(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(t){return t===void 0?"0":Ue(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=jt(t),this._valueRange=jt(this.max)-jt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(jt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}};S(Vs,"id","logarithmic"),S(Vs,"defaults",{ticks:{callback:Os.formatters.logarithmic,major:{enabled:!0}}});function Co(s){let t=s.ticks;if(t.display&&s.display){let e=rt(t.backdropPadding);return I(t.font&&t.font.size,U.font.size)+e.height}return 0}function cm(s,t,e){return e=B(e)?e:[e],{w:ea(s,t.string,e),h:e.length*t.lineHeight}}function cl(s,t,e,i,n){return s===i||s===n?{start:t-e/2,end:t+e/2}:s<i||s>n?{start:t-e,end:t}:{start:t,end:t+e}}function hm(s){let t={l:s.left+s._padding.left,r:s.right-s._padding.right,t:s.top+s._padding.top,b:s.bottom-s._padding.bottom},e=Object.assign({},t),i=[],n=[],o=s._pointLabels.length,r=s.options.pointLabels,a=r.centerPointLabels?$/o:0;for(let l=0;l<o;l++){let c=r.setContext(s.getPointLabelContext(l));n[l]=c.padding;let h=s.getPointPosition(l,s.drawingArea+n[l],a),u=Q(c.font),d=cm(s.ctx,u,s._pointLabels[l]);i[l]=d;let f=ht(s.getIndexAngle(l)+a),m=Math.round(Ri(f)),g=cl(m,h.x,d.w,0,180),p=cl(m,h.y,d.h,90,270);um(e,t,f,g,p)}s.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),s._pointLabelItems=mm(s,i,n)}function um(s,t,e,i,n){let o=Math.abs(Math.sin(e)),r=Math.abs(Math.cos(e)),a=0,l=0;i.start<t.l?(a=(t.l-i.start)/o,s.l=Math.min(s.l,t.l-a)):i.end>t.r&&(a=(i.end-t.r)/o,s.r=Math.max(s.r,t.r+a)),n.start<t.t?(l=(t.t-n.start)/r,s.t=Math.min(s.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/r,s.b=Math.max(s.b,t.b+l))}function dm(s,t,e){let i=s.drawingArea,{extra:n,additionalAngle:o,padding:r,size:a}=e,l=s.getPointPosition(t,i+n+r,o),c=Math.round(Ri(ht(l.angle+X))),h=ym(l.y,a.h,c),u=gm(c),d=pm(l.x,a.w,u);return{visible:!0,x:l.x,y:h,textAlign:u,left:d,top:h,right:d+a.w,bottom:h+a.h}}function fm(s,t){if(!t)return!0;let{left:e,top:i,right:n,bottom:o}=s;return!(Pt({x:e,y:i},t)||Pt({x:e,y:o},t)||Pt({x:n,y:i},t)||Pt({x:n,y:o},t))}function mm(s,t,e){let i=[],n=s._pointLabels.length,o=s.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:Co(o)/2,additionalAngle:r?$/n:0},c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];let u=dm(s,h,l);i.push(u),a==="auto"&&(u.visible=fm(u,c),u.visible&&(c=u))}return i}function gm(s){return s===0||s===180?"center":s<180?"left":"right"}function pm(s,t,e){return e==="right"?s-=t:e==="center"&&(s-=t/2),s}function ym(s,t,e){return e===90||e===270?s-=t/2:(e>270||e<90)&&(s-=t),s}function bm(s,t,e){let{left:i,top:n,right:o,bottom:r}=e,{backdropColor:a}=t;if(!A(a)){let l=re(t.borderRadius),c=rt(t.backdropPadding);s.fillStyle=a;let h=i-c.left,u=n-c.top,d=o-i+c.width,f=r-n+c.height;Object.values(l).some(m=>m!==0)?(s.beginPath(),Ye(s,{x:h,y:u,w:d,h:f,radius:l}),s.fill()):s.fillRect(h,u,d,f)}}function xm(s,t){let{ctx:e,options:{pointLabels:i}}=s;for(let n=t-1;n>=0;n--){let o=s._pointLabelItems[n];if(!o.visible)continue;let r=i.setContext(s.getPointLabelContext(n));bm(e,r,o);let a=Q(r.font),{x:l,y:c,textAlign:h}=o;oe(e,s._pointLabels[n],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function Ll(s,t,e,i){let{ctx:n}=s;if(e)n.arc(s.xCenter,s.yCenter,t,0,j);else{let o=s.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let r=1;r<i;r++)o=s.getPointPosition(r,t),n.lineTo(o.x,o.y)}}function _m(s,t,e,i,n){let o=s.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!i||!a||!l||e<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),Ll(s,e,r,i),o.closePath(),o.stroke(),o.restore())}function wm(s,t,e){return Yt(s,{label:e,index:t,type:"pointLabel"})}var Te=class extends is{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=rt(Co(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=Z(t)&&!isNaN(t)?t:0,this.max=Z(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Co(this.options))}generateTickLabels(t){is.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,i)=>{let n=H(this.options.pointLabels.callback,[e,i],this);return n||n===0?n:""}).filter((e,i)=>this.chart.getDataVisibility(i))}fit(){let t=this.options;t.display&&t.pointLabels.display?hm(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,n))}getIndexAngle(t){let e=j/(this._pointLabels.length||1),i=this.options.startAngle||0;return ht(t*e+wt(i))}getDistanceFromCenterForValue(t){if(A(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(A(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){let i=e[t];return wm(this.getContext(),t,i)}}getPointPosition(t,e,i=0){let n=this.getIndexAngle(t)-X+i;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:i,right:n,bottom:o}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),Ll(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t=this.ctx,e=this.options,{angleLines:i,grid:n,border:o}=e,r=this._pointLabels.length,a,l,c;if(e.pointLabels.display&&xm(this,r),n.display&&this.ticks.forEach((h,u)=>{if(u!==0||u===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);let d=this.getContext(u),f=n.setContext(d),m=o.setContext(d);_m(this,f,l,r,m)}}),i.display){for(t.save(),a=r-1;a>=0;a--){let h=i.setContext(this.getPointLabelContext(a)),{color:u,lineWidth:d}=h;!d||!u||(t.lineWidth=d,t.strokeStyle=u,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){let t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;let n=this.getIndexAngle(0),o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;let c=i.setContext(this.getContext(l)),h=Q(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;let u=rt(c.backdropPadding);t.fillRect(-r/2-u.left,-o-h.size/2-u.top,r+u.width,h.size+u.height)}oe(t,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}};S(Te,"id","radialLinear"),S(Te,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Os.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),S(Te,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),S(Te,"descriptors",{angleLines:{_fallback:"grid"}});var ln={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},ft=Object.keys(ln);function hl(s,t){return s-t}function ul(s,t){if(A(t))return null;let e=s._adapter,{parser:i,round:n,isoWeekday:o}=s._parseOpts,r=t;return typeof i=="function"&&(r=i(r)),Z(r)||(r=typeof i=="string"?e.parse(r,i):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(we(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function dl(s,t,e,i){let n=ft.length;for(let o=ft.indexOf(s);o<n-1;++o){let r=ln[ft[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=i)return ft[o]}return ft[n-1]}function Sm(s,t,e,i,n){for(let o=ft.length-1;o>=ft.indexOf(e);o--){let r=ft[o];if(ln[r].common&&s._adapter.diff(n,i,r)>=t-1)return r}return ft[e?ft.indexOf(e):0]}function km(s){for(let t=ft.indexOf(s)+1,e=ft.length;t<e;++t)if(ln[ft[t]].common)return ft[t]}function fl(s,t,e){if(!e)s[t]=!0;else if(e.length){let{lo:i,hi:n}=Wi(e,t),o=e[i]>=t?e[i]:e[n];s[o]=!0}}function Mm(s,t,e,i){let n=s._adapter,o=+n.startOf(t[0].value,i),r=t[t.length-1].value,a,l;for(a=o;a<=r;a=+n.add(a,1,i))l=e[a],l>=0&&(t[l].major=!0);return t}function ml(s,t,e){let i=[],n={},o=t.length,r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,i.push({value:a,major:!1});return o===0||!e?i:Mm(s,i,n,e)}var De=class extends Ee{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),n=this._adapter=new Fo._date(t.adapters.date);n.init(e),He(i.displayFormats,n.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:ul(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=Z(n)&&!isNaN(n)?n:+e.startOf(Date.now(),i),o=Z(o)&&!isNaN(o)?o:+e.endOf(Date.now(),i)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,n=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);let o=this.min,r=this.max,a=Gr(n,o,r);return this._unit=e.unit||(i.autoSkip?dl(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Sm(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:km(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),ml(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,i=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?i=o:i=(o-this.getDecimalForValue(t[t.length-2]))/2);let r=t.length<3?.5:.25;e=tt(e,0,r),i=tt(i,0,r),this._offsets={start:e,end:i,factor:1/(e+1+i)}}_generate(){let t=this._adapter,e=this.min,i=this.max,n=this.options,o=n.time,r=o.unit||dl(o.minUnit,e,i,this._getLabelCapacity(e)),a=I(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=we(l)||l===!0,h={},u=e,d,f;if(c&&(u=+t.startOf(u,"isoWeek",l)),u=+t.startOf(u,c?"day":r),t.diff(i,e,r)>1e5*a)throw new Error(e+" and "+i+" are too far apart with stepSize of "+a+" "+r);let m=n.ticks.source==="data"&&this.getDataTimestamps();for(d=u,f=0;d<i;d=+t.add(d,a,r),f++)fl(h,d,m);return(d===i||n.bounds==="ticks"||f===1)&&fl(h,d,m),Object.keys(h).sort(hl).map(g=>+g)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,i,n){let o=this.options,r=o.ticks.callback;if(r)return H(r,[t,e,i],this);let a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],u=c&&a[c],d=i[e],f=c&&u&&d&&d.major;return this._adapter.format(t,n||(f?u:h))}generateTickLabels(t){let e,i,n;for(e=0,i=t.length;e<i;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,n=wt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:i*o+a*r,h:i*r+a*o}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,n=i[e.unit]||i.millisecond,o=this._tickFormatFunction(t,0,ml(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,i;if(t.length)return t;let n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,i=n.length;e<i;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){let t=this._cache.labels||[],e,i;if(t.length)return t;let n=this.getLabels();for(e=0,i=n.length;e<i;++e)t.push(ul(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Vn(t.sort(hl))}};S(De,"id","time"),S(De,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Ji(s,t,e){let i=0,n=s.length-1,o,r,a,l;e?(t>=s[i].pos&&t<=s[n].pos&&({lo:i,hi:n}=Lt(s,"pos",t)),{pos:o,time:a}=s[i],{pos:r,time:l}=s[n]):(t>=s[i].time&&t<=s[n].time&&({lo:i,hi:n}=Lt(s,"time",t)),{time:o,pos:a}=s[i],{time:r,pos:l}=s[n]);let c=r-o;return c?a+(l-a)*(t-o)/c:a}var Hs=class extends De{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Ji(e,this.min),this._tableRange=Ji(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let{min:e,max:i}=this,n=[],o=[],r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=i&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return(!i.includes(t)||!i.length)&&i.splice(0,0,t),(!i.includes(e)||i.length===1)&&i.push(e),i.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return e.length&&i.length?t=this.normalize(e.concat(i)):t=e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(Ji(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return Ji(this._table,i*this._tableRange+this._minPos,!0)}};S(Hs,"id","timeseries"),S(Hs,"defaults",De.defaults);var vm=Object.freeze({__proto__:null,CategoryScale:Ws,LinearScale:zs,LogarithmicScale:Vs,RadialLinearScale:Te,TimeScale:De,TimeSeriesScale:Hs}),Pl=[Ru,af,sm,vm];St.register(...Pl);var Ht=St;var Xt=class extends Error{},cn=class extends Xt{constructor(t){super(`Invalid DateTime: ${t.toMessage()}`)}},hn=class extends Xt{constructor(t){super(`Invalid Interval: ${t.toMessage()}`)}},un=class extends Xt{constructor(t){super(`Invalid Duration: ${t.toMessage()}`)}},Dt=class extends Xt{},ns=class extends Xt{constructor(t){super(`Invalid unit ${t}`)}},K=class extends Xt{},Et=class extends Xt{constructor(){super("Zone is an abstract class")}};var T="numeric",It="short",bt="long",ce={year:T,month:T,day:T},Zs={year:T,month:It,day:T},Lo={year:T,month:It,day:T,weekday:It},qs={year:T,month:bt,day:T},Gs={year:T,month:bt,day:T,weekday:bt},Xs={hour:T,minute:T},Ks={hour:T,minute:T,second:T},Js={hour:T,minute:T,second:T,timeZoneName:It},Qs={hour:T,minute:T,second:T,timeZoneName:bt},ti={hour:T,minute:T,hourCycle:"h23"},ei={hour:T,minute:T,second:T,hourCycle:"h23"},si={hour:T,minute:T,second:T,hourCycle:"h23",timeZoneName:It},ii={hour:T,minute:T,second:T,hourCycle:"h23",timeZoneName:bt},ni={year:T,month:T,day:T,hour:T,minute:T},oi={year:T,month:T,day:T,hour:T,minute:T,second:T},ri={year:T,month:It,day:T,hour:T,minute:T},ai={year:T,month:It,day:T,hour:T,minute:T,second:T},Po={year:T,month:It,day:T,weekday:It,hour:T,minute:T},li={year:T,month:bt,day:T,hour:T,minute:T,timeZoneName:It},ci={year:T,month:bt,day:T,hour:T,minute:T,second:T,timeZoneName:It},hi={year:T,month:bt,day:T,weekday:bt,hour:T,minute:T,timeZoneName:bt},ui={year:T,month:bt,day:T,weekday:bt,hour:T,minute:T,second:T,timeZoneName:bt};var mt=class{get type(){throw new Et}get name(){throw new Et}get ianaName(){return this.name}get isUniversal(){throw new Et}offsetName(t,e){throw new Et}formatOffset(t,e){throw new Et}offset(t){throw new Et}equals(t){throw new Et}get isValid(){throw new Et}};var No=null,he=class s extends mt{static get instance(){return No===null&&(No=new s),No}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(t,{format:e,locale:i}){return fn(t,e,i)}formatOffset(t,e){return ue(this.offset(t),e)}offset(t){return-new Date(t).getTimezoneOffset()}equals(t){return t.type==="system"}get isValid(){return!0}};var Wo=new Map;function Tm(s){let t=Wo.get(s);return t===void 0&&(t=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:s,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"}),Wo.set(s,t)),t}var Om={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function Dm(s,t){let e=s.format(t).replace(/\u200E/g,""),i=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(e),[,n,o,r,a,l,c,h]=i;return[r,n,o,a,l,c,h]}function Em(s,t){let e=s.formatToParts(t),i=[];for(let n=0;n<e.length;n++){let{type:o,value:r}=e[n],a=Om[o];o==="era"?i[a]=r:D(a)||(i[a]=parseInt(r,10))}return i}var Ro=new Map,ct=class s extends mt{static create(t){let e=Ro.get(t);return e===void 0&&Ro.set(t,e=new s(t)),e}static resetCache(){Ro.clear(),Wo.clear()}static isValidSpecifier(t){return this.isValidZone(t)}static isValidZone(t){if(!t)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:t}).format(),!0}catch{return!1}}constructor(t){super(),this.zoneName=t,this.valid=s.isValidZone(t)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(t,{format:e,locale:i}){return fn(t,e,i,this.name)}formatOffset(t,e){return ue(this.offset(t),e)}offset(t){if(!this.valid)return NaN;let e=new Date(t);if(isNaN(e))return NaN;let i=Tm(this.name),[n,o,r,a,l,c,h]=i.formatToParts?Em(i,e):Dm(i,e);a==="BC"&&(n=-Math.abs(n)+1);let d=os({year:n,month:o,day:r,hour:l===24?0:l,minute:c,second:h,millisecond:0}),f=+e,m=f%1e3;return f-=m>=0?m:1e3+m,(d-f)/(60*1e3)}equals(t){return t.type==="iana"&&t.name===this.name}get isValid(){return this.valid}};var Nl={};function Im(s,t={}){let e=JSON.stringify([s,t]),i=Nl[e];return i||(i=new Intl.ListFormat(s,t),Nl[e]=i),i}var zo=new Map;function Vo(s,t={}){let e=JSON.stringify([s,t]),i=zo.get(e);return i===void 0&&(i=new Intl.DateTimeFormat(s,t),zo.set(e,i)),i}var Ho=new Map;function Cm(s,t={}){let e=JSON.stringify([s,t]),i=Ho.get(e);return i===void 0&&(i=new Intl.NumberFormat(s,t),Ho.set(e,i)),i}var Bo=new Map;function Fm(s,t={}){let{base:e,...i}=t,n=JSON.stringify([s,i]),o=Bo.get(n);return o===void 0&&(o=new Intl.RelativeTimeFormat(s,t),Bo.set(n,o)),o}var di=null;function Am(){return di||(di=new Intl.DateTimeFormat().resolvedOptions().locale,di)}var $o=new Map;function Rl(s){let t=$o.get(s);return t===void 0&&(t=new Intl.DateTimeFormat(s).resolvedOptions(),$o.set(s,t)),t}var jo=new Map;function Lm(s){let t=jo.get(s);if(!t){let e=new Intl.Locale(s);t="getWeekInfo"in e?e.getWeekInfo():e.weekInfo,"minimalDays"in t||(t={...Wl,...t}),jo.set(s,t)}return t}function Pm(s){let t=s.indexOf("-x-");t!==-1&&(s=s.substring(0,t));let e=s.indexOf("-u-");if(e===-1)return[s];{let i,n;try{i=Vo(s).resolvedOptions(),n=s}catch{let l=s.substring(0,e);i=Vo(l).resolvedOptions(),n=l}let{numberingSystem:o,calendar:r}=i;return[n,o,r]}}function Nm(s,t,e){return(e||t)&&(s.includes("-u-")||(s+="-u"),e&&(s+=`-ca-${e}`),t&&(s+=`-nu-${t}`)),s}function Rm(s){let t=[];for(let e=1;e<=12;e++){let i=F.utc(2009,e,1);t.push(s(i))}return t}function Wm(s){let t=[];for(let e=1;e<=7;e++){let i=F.utc(2016,11,13+e);t.push(s(i))}return t}function mn(s,t,e,i){let n=s.listingMode();return n==="error"?null:n==="en"?e(t):i(t)}function zm(s){return s.numberingSystem&&s.numberingSystem!=="latn"?!1:s.numberingSystem==="latn"||!s.locale||s.locale.startsWith("en")||Rl(s.locale).numberingSystem==="latn"}var Uo=class{constructor(t,e,i){this.padTo=i.padTo||0,this.floor=i.floor||!1;let{padTo:n,floor:o,...r}=i;if(!e||Object.keys(r).length>0){let a={useGrouping:!1,...i};i.padTo>0&&(a.minimumIntegerDigits=i.padTo),this.inf=Cm(t,a)}}format(t){if(this.inf){let e=this.floor?Math.floor(t):t;return this.inf.format(e)}else{let e=this.floor?Math.floor(t):rs(t,3);return q(e,this.padTo)}}},Yo=class{constructor(t,e,i){this.opts=i,this.originalZone=void 0;let n;if(this.opts.timeZone)this.dt=t;else if(t.zone.type==="fixed"){let r=-1*(t.offset/60),a=r>=0?`Etc/GMT+${r}`:`Etc/GMT${r}`;t.offset!==0&&ct.create(a).valid?(n=a,this.dt=t):(n="UTC",this.dt=t.offset===0?t:t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone)}else t.zone.type==="system"?this.dt=t:t.zone.type==="iana"?(this.dt=t,n=t.zone.name):(n="UTC",this.dt=t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone);let o={...this.opts};o.timeZone=o.timeZone||n,this.dtf=Vo(e,o)}format(){return this.originalZone?this.formatToParts().map(({value:t})=>t).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let t=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?t.map(e=>{if(e.type==="timeZoneName"){let i=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...e,value:i}}else return e}):t}resolvedOptions(){return this.dtf.resolvedOptions()}},Zo=class{constructor(t,e,i){this.opts={style:"long",...i},!e&&gn()&&(this.rtf=Fm(t,i))}format(t,e){return this.rtf?this.rtf.format(t,e):zl(e,t,this.opts.numeric,this.opts.style!=="long")}formatToParts(t,e){return this.rtf?this.rtf.formatToParts(t,e):[]}},Wl={firstDay:1,minimalDays:4,weekend:[6,7]},R=class s{static fromOpts(t){return s.create(t.locale,t.numberingSystem,t.outputCalendar,t.weekSettings,t.defaultToEN)}static create(t,e,i,n,o=!1){let r=t||N.defaultLocale,a=r||(o?"en-US":Am()),l=e||N.defaultNumberingSystem,c=i||N.defaultOutputCalendar,h=fi(n)||N.defaultWeekSettings;return new s(a,l,c,h,r)}static resetCache(){di=null,zo.clear(),Ho.clear(),Bo.clear(),$o.clear(),jo.clear()}static fromObject({locale:t,numberingSystem:e,outputCalendar:i,weekSettings:n}={}){return s.create(t,e,i,n)}constructor(t,e,i,n,o){let[r,a,l]=Pm(t);this.locale=r,this.numberingSystem=e||a||null,this.outputCalendar=i||l||null,this.weekSettings=n,this.intl=Nm(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=o,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=zm(this)),this.fastNumbersCached}listingMode(){let t=this.isEnglish(),e=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return t&&e?"en":"intl"}clone(t){return!t||Object.getOwnPropertyNames(t).length===0?this:s.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,fi(t.weekSettings)||this.weekSettings,t.defaultToEN||!1)}redefaultToEN(t={}){return this.clone({...t,defaultToEN:!0})}redefaultToSystem(t={}){return this.clone({...t,defaultToEN:!1})}months(t,e=!1){return mn(this,t,qo,()=>{let i=e?{month:t,day:"numeric"}:{month:t},n=e?"format":"standalone";return this.monthsCache[n][t]||(this.monthsCache[n][t]=Rm(o=>this.extract(o,i,"month"))),this.monthsCache[n][t]})}weekdays(t,e=!1){return mn(this,t,Go,()=>{let i=e?{weekday:t,year:"numeric",month:"long",day:"numeric"}:{weekday:t},n=e?"format":"standalone";return this.weekdaysCache[n][t]||(this.weekdaysCache[n][t]=Wm(o=>this.extract(o,i,"weekday"))),this.weekdaysCache[n][t]})}meridiems(){return mn(this,void 0,()=>Xo,()=>{if(!this.meridiemCache){let t={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[F.utc(2016,11,13,9),F.utc(2016,11,13,19)].map(e=>this.extract(e,t,"dayperiod"))}return this.meridiemCache})}eras(t){return mn(this,t,Ko,()=>{let e={era:t};return this.eraCache[t]||(this.eraCache[t]=[F.utc(-40,1,1),F.utc(2017,1,1)].map(i=>this.extract(i,e,"era"))),this.eraCache[t]})}extract(t,e,i){let n=this.dtFormatter(t,e),o=n.formatToParts(),r=o.find(a=>a.type.toLowerCase()===i);return r?r.value:null}numberFormatter(t={}){return new Uo(this.intl,t.forceSimple||this.fastNumbers,t)}dtFormatter(t,e={}){return new Yo(t,this.intl,e)}relFormatter(t={}){return new Zo(this.intl,this.isEnglish(),t)}listFormatter(t={}){return Im(this.intl,t)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||Rl(this.intl).locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:pn()?Lm(this.locale):Wl}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(t){return this.locale===t.locale&&this.numberingSystem===t.numberingSystem&&this.outputCalendar===t.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}};var Qo=null,et=class s extends mt{static get utcInstance(){return Qo===null&&(Qo=new s(0)),Qo}static instance(t){return t===0?s.utcInstance:new s(t)}static parseSpecifier(t){if(t){let e=t.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(e)return new s(Ie(e[1],e[2]))}return null}constructor(t){super(),this.fixed=t}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${ue(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${ue(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(t,e){return ue(this.fixed,e)}get isUniversal(){return!0}offset(){return this.fixed}equals(t){return t.type==="fixed"&&t.fixed===this.fixed}get isValid(){return!0}};var as=class extends mt{constructor(t){super(),this.zoneName=t}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function Ct(s,t){let e;if(D(s)||s===null)return t;if(s instanceof mt)return s;if(Vl(s)){let i=s.toLowerCase();return i==="default"?t:i==="local"||i==="system"?he.instance:i==="utc"||i==="gmt"?et.utcInstance:et.parseSpecifier(i)||ct.create(s)}else return Ft(s)?et.instance(s):typeof s=="object"&&"offset"in s&&typeof s.offset=="function"?s:new as(s)}var er={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},Hl={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},Vm=er.hanidec.replace(/[\[|\]]/g,"").split("");function Bl(s){let t=parseInt(s,10);if(isNaN(t)){t="";for(let e=0;e<s.length;e++){let i=s.charCodeAt(e);if(s[e].search(er.hanidec)!==-1)t+=Vm.indexOf(s[e]);else for(let n in Hl){let[o,r]=Hl[n];i>=o&&i<=r&&(t+=i-o)}}return parseInt(t,10)}else return t}var tr=new Map;function $l(){tr.clear()}function kt({numberingSystem:s},t=""){let e=s||"latn",i=tr.get(e);i===void 0&&(i=new Map,tr.set(e,i));let n=i.get(t);return n===void 0&&(n=new RegExp(`${er[e]}${t}`),i.set(t,n)),n}var jl=()=>Date.now(),Ul="system",Yl=null,Zl=null,ql=null,Gl=60,Xl,Kl=null,N=class{static get now(){return jl}static set now(t){jl=t}static set defaultZone(t){Ul=t}static get defaultZone(){return Ct(Ul,he.instance)}static get defaultLocale(){return Yl}static set defaultLocale(t){Yl=t}static get defaultNumberingSystem(){return Zl}static set defaultNumberingSystem(t){Zl=t}static get defaultOutputCalendar(){return ql}static set defaultOutputCalendar(t){ql=t}static get defaultWeekSettings(){return Kl}static set defaultWeekSettings(t){Kl=fi(t)}static get twoDigitCutoffYear(){return Gl}static set twoDigitCutoffYear(t){Gl=t%100}static get throwOnInvalid(){return Xl}static set throwOnInvalid(t){Xl=t}static resetCaches(){R.resetCache(),ct.resetCache(),F.resetCache(),$l()}};var st=class{constructor(t,e){this.reason=t,this.explanation=e}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}};var Jl=[0,31,59,90,120,151,181,212,243,273,304,334],Ql=[0,31,60,91,121,152,182,213,244,274,305,335];function Mt(s,t){return new st("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${s}, which is invalid`)}function yn(s,t,e){let i=new Date(Date.UTC(s,t-1,e));s<100&&s>=0&&i.setUTCFullYear(i.getUTCFullYear()-1900);let n=i.getUTCDay();return n===0?7:n}function tc(s,t,e){return e+(Fe(s)?Ql:Jl)[t-1]}function ec(s,t){let e=Fe(s)?Ql:Jl,i=e.findIndex(o=>o<t),n=t-e[i];return{month:i+1,day:n}}function bn(s,t){return(s-t+7)%7+1}function mi(s,t=4,e=1){let{year:i,month:n,day:o}=s,r=tc(i,n,o),a=bn(yn(i,n,o),e),l=Math.floor((r-a+14-t)/7),c;return l<1?(c=i-1,l=Ce(c,t,e)):l>Ce(i,t,e)?(c=i+1,l=1):c=i,{weekYear:c,weekNumber:l,weekday:a,...pi(s)}}function sr(s,t=4,e=1){let{weekYear:i,weekNumber:n,weekday:o}=s,r=bn(yn(i,1,t),e),a=de(i),l=n*7+o-r-7+t,c;l<1?(c=i-1,l+=de(c)):l>a?(c=i+1,l-=de(i)):c=i;let{month:h,day:u}=ec(c,l);return{year:c,month:h,day:u,...pi(s)}}function xn(s){let{year:t,month:e,day:i}=s,n=tc(t,e,i);return{year:t,ordinal:n,...pi(s)}}function ir(s){let{year:t,ordinal:e}=s,{month:i,day:n}=ec(t,e);return{year:t,month:i,day:n,...pi(s)}}function nr(s,t){if(!D(s.localWeekday)||!D(s.localWeekNumber)||!D(s.localWeekYear)){if(!D(s.weekday)||!D(s.weekNumber)||!D(s.weekYear))throw new Dt("Cannot mix locale-based week fields with ISO-based week fields");return D(s.localWeekday)||(s.weekday=s.localWeekday),D(s.localWeekNumber)||(s.weekNumber=s.localWeekNumber),D(s.localWeekYear)||(s.weekYear=s.localWeekYear),delete s.localWeekday,delete s.localWeekNumber,delete s.localWeekYear,{minDaysInFirstWeek:t.getMinDaysInFirstWeek(),startOfWeek:t.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function sc(s,t=4,e=1){let i=gi(s.weekYear),n=xt(s.weekNumber,1,Ce(s.weekYear,t,e)),o=xt(s.weekday,1,7);return i?n?o?!1:Mt("weekday",s.weekday):Mt("week",s.weekNumber):Mt("weekYear",s.weekYear)}function ic(s){let t=gi(s.year),e=xt(s.ordinal,1,de(s.year));return t?e?!1:Mt("ordinal",s.ordinal):Mt("year",s.year)}function or(s){let t=gi(s.year),e=xt(s.month,1,12),i=xt(s.day,1,ls(s.year,s.month));return t?e?i?!1:Mt("day",s.day):Mt("month",s.month):Mt("year",s.year)}function rr(s){let{hour:t,minute:e,second:i,millisecond:n}=s,o=xt(t,0,23)||t===24&&e===0&&i===0&&n===0,r=xt(e,0,59),a=xt(i,0,59),l=xt(n,0,999);return o?r?a?l?!1:Mt("millisecond",n):Mt("second",i):Mt("minute",e):Mt("hour",t)}function D(s){return typeof s>"u"}function Ft(s){return typeof s=="number"}function gi(s){return typeof s=="number"&&s%1===0}function Vl(s){return typeof s=="string"}function oc(s){return Object.prototype.toString.call(s)==="[object Date]"}function gn(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function pn(){try{return typeof Intl<"u"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch{return!1}}function rc(s){return Array.isArray(s)?s:[s]}function ar(s,t,e){if(s.length!==0)return s.reduce((i,n)=>{let o=[t(n),n];return i&&e(i[0],o[0])===i[0]?i:o},null)[1]}function ac(s,t){return t.reduce((e,i)=>(e[i]=s[i],e),{})}function fe(s,t){return Object.prototype.hasOwnProperty.call(s,t)}function fi(s){if(s==null)return null;if(typeof s!="object")throw new K("Week settings must be an object");if(!xt(s.firstDay,1,7)||!xt(s.minimalDays,1,7)||!Array.isArray(s.weekend)||s.weekend.some(t=>!xt(t,1,7)))throw new K("Invalid week settings");return{firstDay:s.firstDay,minimalDays:s.minimalDays,weekend:Array.from(s.weekend)}}function xt(s,t,e){return gi(s)&&s>=t&&s<=e}function Hm(s,t){return s-t*Math.floor(s/t)}function q(s,t=2){let e=s<0,i;return e?i="-"+(""+-s).padStart(t,"0"):i=(""+s).padStart(t,"0"),i}function Kt(s){if(!(D(s)||s===null||s===""))return parseInt(s,10)}function me(s){if(!(D(s)||s===null||s===""))return parseFloat(s)}function yi(s){if(!(D(s)||s===null||s==="")){let t=parseFloat("0."+s)*1e3;return Math.floor(t)}}function rs(s,t,e=!1){let i=10**t;return(e?Math.trunc:Math.round)(s*i)/i}function Fe(s){return s%4===0&&(s%100!==0||s%400===0)}function de(s){return Fe(s)?366:365}function ls(s,t){let e=Hm(t-1,12)+1,i=s+(t-e)/12;return e===2?Fe(i)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][e-1]}function os(s){let t=Date.UTC(s.year,s.month-1,s.day,s.hour,s.minute,s.second,s.millisecond);return s.year<100&&s.year>=0&&(t=new Date(t),t.setUTCFullYear(s.year,s.month-1,s.day)),+t}function nc(s,t,e){return-bn(yn(s,1,t),e)+t-1}function Ce(s,t=4,e=1){let i=nc(s,t,e),n=nc(s+1,t,e);return(de(s)-i+n)/7}function bi(s){return s>99?s:s>N.twoDigitCutoffYear?1900+s:2e3+s}function fn(s,t,e,i=null){let n=new Date(s),o={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};i&&(o.timeZone=i);let r={timeZoneName:t,...o},a=new Intl.DateTimeFormat(e,r).formatToParts(n).find(l=>l.type.toLowerCase()==="timezonename");return a?a.value:null}function Ie(s,t){let e=parseInt(s,10);Number.isNaN(e)&&(e=0);let i=parseInt(t,10)||0,n=e<0||Object.is(e,-0)?-i:i;return e*60+n}function lr(s){let t=Number(s);if(typeof s=="boolean"||s===""||Number.isNaN(t))throw new K(`Invalid unit value ${s}`);return t}function cs(s,t){let e={};for(let i in s)if(fe(s,i)){let n=s[i];if(n==null)continue;e[t(i)]=lr(n)}return e}function ue(s,t){let e=Math.trunc(Math.abs(s/60)),i=Math.trunc(Math.abs(s%60)),n=s>=0?"+":"-";switch(t){case"short":return`${n}${q(e,2)}:${q(i,2)}`;case"narrow":return`${n}${e}${i>0?`:${i}`:""}`;case"techie":return`${n}${q(e,2)}${q(i,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function pi(s){return ac(s,["hour","minute","second","millisecond"])}var Bm=["January","February","March","April","May","June","July","August","September","October","November","December"],cr=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],$m=["J","F","M","A","M","J","J","A","S","O","N","D"];function qo(s){switch(s){case"narrow":return[...$m];case"short":return[...cr];case"long":return[...Bm];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var hr=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],ur=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],jm=["M","T","W","T","F","S","S"];function Go(s){switch(s){case"narrow":return[...jm];case"short":return[...ur];case"long":return[...hr];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var Xo=["AM","PM"],Um=["Before Christ","Anno Domini"],Ym=["BC","AD"],Zm=["B","A"];function Ko(s){switch(s){case"narrow":return[...Zm];case"short":return[...Ym];case"long":return[...Um];default:return null}}function lc(s){return Xo[s.hour<12?0:1]}function cc(s,t){return Go(t)[s.weekday-1]}function hc(s,t){return qo(t)[s.month-1]}function uc(s,t){return Ko(t)[s.year<0?0:1]}function zl(s,t,e="always",i=!1){let n={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},o=["hours","minutes","seconds"].indexOf(s)===-1;if(e==="auto"&&o){let u=s==="days";switch(t){case 1:return u?"tomorrow":`next ${n[s][0]}`;case-1:return u?"yesterday":`last ${n[s][0]}`;case 0:return u?"today":`this ${n[s][0]}`;default:}}let r=Object.is(t,-0)||t<0,a=Math.abs(t),l=a===1,c=n[s],h=i?l?c[1]:c[2]||c[1]:l?n[s][0]:s;return r?`${a} ${h} ago`:`in ${a} ${h}`}function dc(s,t){let e="";for(let i of s)i.literal?e+=i.val:e+=t(i.val);return e}var qm={D:ce,DD:Zs,DDD:qs,DDDD:Gs,t:Xs,tt:Ks,ttt:Js,tttt:Qs,T:ti,TT:ei,TTT:si,TTTT:ii,f:ni,ff:ri,fff:li,ffff:hi,F:oi,FF:ai,FFF:ci,FFFF:ui},it=class s{static create(t,e={}){return new s(t,e)}static parseFormat(t){let e=null,i="",n=!1,o=[];for(let r=0;r<t.length;r++){let a=t.charAt(r);a==="'"?(i.length>0&&o.push({literal:n||/^\s+$/.test(i),val:i}),e=null,i="",n=!n):n||a===e?i+=a:(i.length>0&&o.push({literal:/^\s+$/.test(i),val:i}),i=a,e=a)}return i.length>0&&o.push({literal:n||/^\s+$/.test(i),val:i}),o}static macroTokenToFormatOpts(t){return qm[t]}constructor(t,e){this.opts=e,this.loc=t,this.systemLoc=null}formatWithSystemDefault(t,e){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(t,{...this.opts,...e}).format()}dtFormatter(t,e={}){return this.loc.dtFormatter(t,{...this.opts,...e})}formatDateTime(t,e){return this.dtFormatter(t,e).format()}formatDateTimeParts(t,e){return this.dtFormatter(t,e).formatToParts()}formatInterval(t,e){return this.dtFormatter(t.start,e).dtf.formatRange(t.start.toJSDate(),t.end.toJSDate())}resolvedOptions(t,e){return this.dtFormatter(t,e).resolvedOptions()}num(t,e=0){if(this.opts.forceSimple)return q(t,e);let i={...this.opts};return e>0&&(i.padTo=e),this.loc.numberFormatter(i).format(t)}formatDateTimeFromString(t,e){let i=this.loc.listingMode()==="en",n=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",o=(f,m)=>this.loc.extract(t,f,m),r=f=>t.isOffsetFixed&&t.offset===0&&f.allowZ?"Z":t.isValid?t.zone.formatOffset(t.ts,f.format):"",a=()=>i?lc(t):o({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(f,m)=>i?hc(t,f):o(m?{month:f}:{month:f,day:"numeric"},"month"),c=(f,m)=>i?cc(t,f):o(m?{weekday:f}:{weekday:f,month:"long",day:"numeric"},"weekday"),h=f=>{let m=s.macroTokenToFormatOpts(f);return m?this.formatWithSystemDefault(t,m):f},u=f=>i?uc(t,f):o({era:f},"era"),d=f=>{switch(f){case"S":return this.num(t.millisecond);case"u":case"SSS":return this.num(t.millisecond,3);case"s":return this.num(t.second);case"ss":return this.num(t.second,2);case"uu":return this.num(Math.floor(t.millisecond/10),2);case"uuu":return this.num(Math.floor(t.millisecond/100));case"m":return this.num(t.minute);case"mm":return this.num(t.minute,2);case"h":return this.num(t.hour%12===0?12:t.hour%12);case"hh":return this.num(t.hour%12===0?12:t.hour%12,2);case"H":return this.num(t.hour);case"HH":return this.num(t.hour,2);case"Z":return r({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return r({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return r({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return t.zone.offsetName(t.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return t.zone.offsetName(t.ts,{format:"long",locale:this.loc.locale});case"z":return t.zoneName;case"a":return a();case"d":return n?o({day:"numeric"},"day"):this.num(t.day);case"dd":return n?o({day:"2-digit"},"day"):this.num(t.day,2);case"c":return this.num(t.weekday);case"ccc":return c("short",!0);case"cccc":return c("long",!0);case"ccccc":return c("narrow",!0);case"E":return this.num(t.weekday);case"EEE":return c("short",!1);case"EEEE":return c("long",!1);case"EEEEE":return c("narrow",!1);case"L":return n?o({month:"numeric",day:"numeric"},"month"):this.num(t.month);case"LL":return n?o({month:"2-digit",day:"numeric"},"month"):this.num(t.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return n?o({month:"numeric"},"month"):this.num(t.month);case"MM":return n?o({month:"2-digit"},"month"):this.num(t.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return n?o({year:"numeric"},"year"):this.num(t.year);case"yy":return n?o({year:"2-digit"},"year"):this.num(t.year.toString().slice(-2),2);case"yyyy":return n?o({year:"numeric"},"year"):this.num(t.year,4);case"yyyyyy":return n?o({year:"numeric"},"year"):this.num(t.year,6);case"G":return u("short");case"GG":return u("long");case"GGGGG":return u("narrow");case"kk":return this.num(t.weekYear.toString().slice(-2),2);case"kkkk":return this.num(t.weekYear,4);case"W":return this.num(t.weekNumber);case"WW":return this.num(t.weekNumber,2);case"n":return this.num(t.localWeekNumber);case"nn":return this.num(t.localWeekNumber,2);case"ii":return this.num(t.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(t.localWeekYear,4);case"o":return this.num(t.ordinal);case"ooo":return this.num(t.ordinal,3);case"q":return this.num(t.quarter);case"qq":return this.num(t.quarter,2);case"X":return this.num(Math.floor(t.ts/1e3));case"x":return this.num(t.ts);default:return h(f)}};return dc(s.parseFormat(e),d)}formatDurationFromString(t,e){let i=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},n=l=>c=>{let h=i(c);return h?this.num(l.get(h),c.length):c},o=s.parseFormat(e),r=o.reduce((l,{literal:c,val:h})=>c?l:l.concat(h),[]),a=t.shiftTo(...r.map(i).filter(l=>l));return dc(o,n(a))}};var mc=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function us(...s){let t=s.reduce((e,i)=>e+i.source,"");return RegExp(`^${t}$`)}function ds(...s){return t=>s.reduce(([e,i,n],o)=>{let[r,a,l]=o(t,n);return[{...e,...r},a||i,l]},[{},null,1]).slice(0,2)}function fs(s,...t){if(s==null)return[null,null];for(let[e,i]of t){let n=e.exec(s);if(n)return i(n)}return[null,null]}function gc(...s){return(t,e)=>{let i={},n;for(n=0;n<s.length;n++)i[s[n]]=Kt(t[e+n]);return[i,null,e+n]}}var pc=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,Gm=`(?:${pc.source}?(?:\\[(${mc.source})\\])?)?`,dr=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,yc=RegExp(`${dr.source}${Gm}`),fr=RegExp(`(?:T${yc.source})?`),Xm=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,Km=/(\d{4})-?W(\d\d)(?:-?(\d))?/,Jm=/(\d{4})-?(\d{3})/,Qm=gc("weekYear","weekNumber","weekDay"),tg=gc("year","ordinal"),eg=/(\d{4})-(\d\d)-(\d\d)/,bc=RegExp(`${dr.source} ?(?:${pc.source}|(${mc.source}))?`),sg=RegExp(`(?: ${bc.source})?`);function hs(s,t,e){let i=s[t];return D(i)?e:Kt(i)}function ig(s,t){return[{year:hs(s,t),month:hs(s,t+1,1),day:hs(s,t+2,1)},null,t+3]}function ms(s,t){return[{hours:hs(s,t,0),minutes:hs(s,t+1,0),seconds:hs(s,t+2,0),milliseconds:yi(s[t+3])},null,t+4]}function xi(s,t){let e=!s[t]&&!s[t+1],i=Ie(s[t+1],s[t+2]),n=e?null:et.instance(i);return[{},n,t+3]}function _i(s,t){let e=s[t]?ct.create(s[t]):null;return[{},e,t+1]}var ng=RegExp(`^T?${dr.source}$`),og=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function rg(s){let[t,e,i,n,o,r,a,l,c]=s,h=t[0]==="-",u=l&&l[0]==="-",d=(f,m=!1)=>f!==void 0&&(m||f&&h)?-f:f;return[{years:d(me(e)),months:d(me(i)),weeks:d(me(n)),days:d(me(o)),hours:d(me(r)),minutes:d(me(a)),seconds:d(me(l),l==="-0"),milliseconds:d(yi(c),u)}]}var ag={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function mr(s,t,e,i,n,o,r){let a={year:t.length===2?bi(Kt(t)):Kt(t),month:cr.indexOf(e)+1,day:Kt(i),hour:Kt(n),minute:Kt(o)};return r&&(a.second=Kt(r)),s&&(a.weekday=s.length>3?hr.indexOf(s)+1:ur.indexOf(s)+1),a}var lg=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function cg(s){let[,t,e,i,n,o,r,a,l,c,h,u]=s,d=mr(t,n,i,e,o,r,a),f;return l?f=ag[l]:c?f=0:f=Ie(h,u),[d,new et(f)]}function hg(s){return s.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var ug=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,dg=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,fg=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function fc(s){let[,t,e,i,n,o,r,a]=s;return[mr(t,n,i,e,o,r,a),et.utcInstance]}function mg(s){let[,t,e,i,n,o,r,a]=s;return[mr(t,a,e,i,n,o,r),et.utcInstance]}var gg=us(Xm,fr),pg=us(Km,fr),yg=us(Jm,fr),bg=us(yc),xc=ds(ig,ms,xi,_i),xg=ds(Qm,ms,xi,_i),_g=ds(tg,ms,xi,_i),wg=ds(ms,xi,_i);function _c(s){return fs(s,[gg,xc],[pg,xg],[yg,_g],[bg,wg])}function wc(s){return fs(hg(s),[lg,cg])}function Sc(s){return fs(s,[ug,fc],[dg,fc],[fg,mg])}function kc(s){return fs(s,[og,rg])}var Sg=ds(ms);function Mc(s){return fs(s,[ng,Sg])}var kg=us(eg,sg),Mg=us(bc),vg=ds(ms,xi,_i);function vc(s){return fs(s,[kg,xc],[Mg,vg])}var Tc="Invalid Duration",Dc={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},Tg={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Dc},vt=146097/400,gs=146097/4800,Og={years:{quarters:4,months:12,weeks:vt/7,days:vt,hours:vt*24,minutes:vt*24*60,seconds:vt*24*60*60,milliseconds:vt*24*60*60*1e3},quarters:{months:3,weeks:vt/28,days:vt/4,hours:vt*24/4,minutes:vt*24*60/4,seconds:vt*24*60*60/4,milliseconds:vt*24*60*60*1e3/4},months:{weeks:gs/7,days:gs,hours:gs*24,minutes:gs*24*60,seconds:gs*24*60*60,milliseconds:gs*24*60*60*1e3},...Dc},Ae=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Dg=Ae.slice(0).reverse();function ge(s,t,e=!1){let i={values:e?t.values:{...s.values,...t.values||{}},loc:s.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||s.conversionAccuracy,matrix:t.matrix||s.matrix};return new G(i)}function Ec(s,t){let e=t.milliseconds??0;for(let i of Dg.slice(1))t[i]&&(e+=t[i]*s[i].milliseconds);return e}function Oc(s,t){let e=Ec(s,t)<0?-1:1;Ae.reduceRight((i,n)=>{if(D(t[n]))return i;if(i){let o=t[i]*e,r=s[n][i],a=Math.floor(o/r);t[n]+=a*e,t[i]-=a*r*e}return n},null),Ae.reduce((i,n)=>{if(D(t[n]))return i;if(i){let o=t[i]%1;t[i]-=o,t[n]+=o*s[i][n]}return n},null)}function Eg(s){let t={};for(let[e,i]of Object.entries(s))i!==0&&(t[e]=i);return t}var G=class s{constructor(t){let e=t.conversionAccuracy==="longterm"||!1,i=e?Og:Tg;t.matrix&&(i=t.matrix),this.values=t.values,this.loc=t.loc||R.create(),this.conversionAccuracy=e?"longterm":"casual",this.invalid=t.invalid||null,this.matrix=i,this.isLuxonDuration=!0}static fromMillis(t,e){return s.fromObject({milliseconds:t},e)}static fromObject(t,e={}){if(t==null||typeof t!="object")throw new K(`Duration.fromObject: argument expected to be an object, got ${t===null?"null":typeof t}`);return new s({values:cs(t,s.normalizeUnit),loc:R.fromObject(e),conversionAccuracy:e.conversionAccuracy,matrix:e.matrix})}static fromDurationLike(t){if(Ft(t))return s.fromMillis(t);if(s.isDuration(t))return t;if(typeof t=="object")return s.fromObject(t);throw new K(`Unknown duration argument ${t} of type ${typeof t}`)}static fromISO(t,e){let[i]=kc(t);return i?s.fromObject(i,e):s.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static fromISOTime(t,e){let[i]=Mc(t);return i?s.fromObject(i,e):s.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static invalid(t,e=null){if(!t)throw new K("need to specify a reason the Duration is invalid");let i=t instanceof st?t:new st(t,e);if(N.throwOnInvalid)throw new un(i);return new s({invalid:i})}static normalizeUnit(t){let e={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[t&&t.toLowerCase()];if(!e)throw new ns(t);return e}static isDuration(t){return t&&t.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(t,e={}){let i={...e,floor:e.round!==!1&&e.floor!==!1};return this.isValid?it.create(this.loc,i).formatDurationFromString(this,t):Tc}toHuman(t={}){if(!this.isValid)return Tc;let e=Ae.map(i=>{let n=this.values[i];return D(n)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...t,unit:i.slice(0,-1)}).format(n)}).filter(i=>i);return this.loc.listFormatter({type:"conjunction",style:t.listStyle||"narrow",...t}).format(e)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let t="P";return this.years!==0&&(t+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(t+=this.months+this.quarters*3+"M"),this.weeks!==0&&(t+=this.weeks+"W"),this.days!==0&&(t+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(t+="T"),this.hours!==0&&(t+=this.hours+"H"),this.minutes!==0&&(t+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(t+=rs(this.seconds+this.milliseconds/1e3,3)+"S"),t==="P"&&(t+="T0S"),t}toISOTime(t={}){if(!this.isValid)return null;let e=this.toMillis();return e<0||e>=864e5?null:(t={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...t,includeOffset:!1},F.fromMillis(e,{zone:"UTC"}).toISOTime(t))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?Ec(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(t){if(!this.isValid)return this;let e=s.fromDurationLike(t),i={};for(let n of Ae)(fe(e.values,n)||fe(this.values,n))&&(i[n]=e.get(n)+this.get(n));return ge(this,{values:i},!0)}minus(t){if(!this.isValid)return this;let e=s.fromDurationLike(t);return this.plus(e.negate())}mapUnits(t){if(!this.isValid)return this;let e={};for(let i of Object.keys(this.values))e[i]=lr(t(this.values[i],i));return ge(this,{values:e},!0)}get(t){return this[s.normalizeUnit(t)]}set(t){if(!this.isValid)return this;let e={...this.values,...cs(t,s.normalizeUnit)};return ge(this,{values:e})}reconfigure({locale:t,numberingSystem:e,conversionAccuracy:i,matrix:n}={}){let r={loc:this.loc.clone({locale:t,numberingSystem:e}),matrix:n,conversionAccuracy:i};return ge(this,r)}as(t){return this.isValid?this.shiftTo(t).get(t):NaN}normalize(){if(!this.isValid)return this;let t=this.toObject();return Oc(this.matrix,t),ge(this,{values:t},!0)}rescale(){if(!this.isValid)return this;let t=Eg(this.normalize().shiftToAll().toObject());return ge(this,{values:t},!0)}shiftTo(...t){if(!this.isValid)return this;if(t.length===0)return this;t=t.map(r=>s.normalizeUnit(r));let e={},i={},n=this.toObject(),o;for(let r of Ae)if(t.indexOf(r)>=0){o=r;let a=0;for(let c in i)a+=this.matrix[c][r]*i[c],i[c]=0;Ft(n[r])&&(a+=n[r]);let l=Math.trunc(a);e[r]=l,i[r]=(a*1e3-l*1e3)/1e3}else Ft(n[r])&&(i[r]=n[r]);for(let r in i)i[r]!==0&&(e[o]+=r===o?i[r]:i[r]/this.matrix[o][r]);return Oc(this.matrix,e),ge(this,{values:e},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let t={};for(let e of Object.keys(this.values))t[e]=this.values[e]===0?0:-this.values[e];return ge(this,{values:t},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(t){if(!this.isValid||!t.isValid||!this.loc.equals(t.loc))return!1;function e(i,n){return i===void 0||i===0?n===void 0||n===0:i===n}for(let i of Ae)if(!e(this.values[i],t.values[i]))return!1;return!0}};var ps="Invalid Interval";function Ig(s,t){return!s||!s.isValid?Jt.invalid("missing or invalid start"):!t||!t.isValid?Jt.invalid("missing or invalid end"):t<s?Jt.invalid("end before start",`The end of an interval must be after its start, but you had start=${s.toISO()} and end=${t.toISO()}`):null}var Jt=class s{constructor(t){this.s=t.start,this.e=t.end,this.invalid=t.invalid||null,this.isLuxonInterval=!0}static invalid(t,e=null){if(!t)throw new K("need to specify a reason the Interval is invalid");let i=t instanceof st?t:new st(t,e);if(N.throwOnInvalid)throw new hn(i);return new s({invalid:i})}static fromDateTimes(t,e){let i=ys(t),n=ys(e),o=Ig(i,n);return o??new s({start:i,end:n})}static after(t,e){let i=G.fromDurationLike(e),n=ys(t);return s.fromDateTimes(n,n.plus(i))}static before(t,e){let i=G.fromDurationLike(e),n=ys(t);return s.fromDateTimes(n.minus(i),n)}static fromISO(t,e){let[i,n]=(t||"").split("/",2);if(i&&n){let o,r;try{o=F.fromISO(i,e),r=o.isValid}catch{r=!1}let a,l;try{a=F.fromISO(n,e),l=a.isValid}catch{l=!1}if(r&&l)return s.fromDateTimes(o,a);if(r){let c=G.fromISO(n,e);if(c.isValid)return s.after(o,c)}else if(l){let c=G.fromISO(i,e);if(c.isValid)return s.before(a,c)}}return s.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static isInterval(t){return t&&t.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get lastDateTime(){return this.isValid&&this.e?this.e.minus(1):null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(t="milliseconds"){return this.isValid?this.toDuration(t).get(t):NaN}count(t="milliseconds",e){if(!this.isValid)return NaN;let i=this.start.startOf(t,e),n;return e?.useLocaleWeeks?n=this.end.reconfigure({locale:i.locale}):n=this.end,n=n.startOf(t,e),Math.floor(n.diff(i,t).get(t))+(n.valueOf()!==this.end.valueOf())}hasSame(t){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,t):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(t){return this.isValid?this.s>t:!1}isBefore(t){return this.isValid?this.e<=t:!1}contains(t){return this.isValid?this.s<=t&&this.e>t:!1}set({start:t,end:e}={}){return this.isValid?s.fromDateTimes(t||this.s,e||this.e):this}splitAt(...t){if(!this.isValid)return[];let e=t.map(ys).filter(r=>this.contains(r)).sort((r,a)=>r.toMillis()-a.toMillis()),i=[],{s:n}=this,o=0;for(;n<this.e;){let r=e[o]||this.e,a=+r>+this.e?this.e:r;i.push(s.fromDateTimes(n,a)),n=a,o+=1}return i}splitBy(t){let e=G.fromDurationLike(t);if(!this.isValid||!e.isValid||e.as("milliseconds")===0)return[];let{s:i}=this,n=1,o,r=[];for(;i<this.e;){let a=this.start.plus(e.mapUnits(l=>l*n));o=+a>+this.e?this.e:a,r.push(s.fromDateTimes(i,o)),i=o,n+=1}return r}divideEqually(t){return this.isValid?this.splitBy(this.length()/t).slice(0,t):[]}overlaps(t){return this.e>t.s&&this.s<t.e}abutsStart(t){return this.isValid?+this.e==+t.s:!1}abutsEnd(t){return this.isValid?+t.e==+this.s:!1}engulfs(t){return this.isValid?this.s<=t.s&&this.e>=t.e:!1}equals(t){return!this.isValid||!t.isValid?!1:this.s.equals(t.s)&&this.e.equals(t.e)}intersection(t){if(!this.isValid)return this;let e=this.s>t.s?this.s:t.s,i=this.e<t.e?this.e:t.e;return e>=i?null:s.fromDateTimes(e,i)}union(t){if(!this.isValid)return this;let e=this.s<t.s?this.s:t.s,i=this.e>t.e?this.e:t.e;return s.fromDateTimes(e,i)}static merge(t){let[e,i]=t.sort((n,o)=>n.s-o.s).reduce(([n,o],r)=>o?o.overlaps(r)||o.abutsStart(r)?[n,o.union(r)]:[n.concat([o]),r]:[n,r],[[],null]);return i&&e.push(i),e}static xor(t){let e=null,i=0,n=[],o=t.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),r=Array.prototype.concat(...o),a=r.sort((l,c)=>l.time-c.time);for(let l of a)i+=l.type==="s"?1:-1,i===1?e=l.time:(e&&+e!=+l.time&&n.push(s.fromDateTimes(e,l.time)),e=null);return s.merge(n)}difference(...t){return s.xor([this].concat(t)).map(e=>this.intersection(e)).filter(e=>e&&!e.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:ps}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(t=ce,e={}){return this.isValid?it.create(this.s.loc.clone(e),t).formatInterval(this):ps}toISO(t){return this.isValid?`${this.s.toISO(t)}/${this.e.toISO(t)}`:ps}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:ps}toISOTime(t){return this.isValid?`${this.s.toISOTime(t)}/${this.e.toISOTime(t)}`:ps}toFormat(t,{separator:e=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(t)}${e}${this.e.toFormat(t)}`:ps}toDuration(t,e){return this.isValid?this.e.diff(this.s,t,e):G.invalid(this.invalidReason)}mapEndpoints(t){return s.fromDateTimes(t(this.s),t(this.e))}};var Qt=class{static hasDST(t=N.defaultZone){let e=F.now().setZone(t).set({month:12});return!t.isUniversal&&e.offset!==e.set({month:6}).offset}static isValidIANAZone(t){return ct.isValidZone(t)}static normalizeZone(t){return Ct(t,N.defaultZone)}static getStartOfWeek({locale:t=null,locObj:e=null}={}){return(e||R.create(t)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:t=null,locObj:e=null}={}){return(e||R.create(t)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:t=null,locObj:e=null}={}){return(e||R.create(t)).getWeekendDays().slice()}static months(t="long",{locale:e=null,numberingSystem:i=null,locObj:n=null,outputCalendar:o="gregory"}={}){return(n||R.create(e,i,o)).months(t)}static monthsFormat(t="long",{locale:e=null,numberingSystem:i=null,locObj:n=null,outputCalendar:o="gregory"}={}){return(n||R.create(e,i,o)).months(t,!0)}static weekdays(t="long",{locale:e=null,numberingSystem:i=null,locObj:n=null}={}){return(n||R.create(e,i,null)).weekdays(t)}static weekdaysFormat(t="long",{locale:e=null,numberingSystem:i=null,locObj:n=null}={}){return(n||R.create(e,i,null)).weekdays(t,!0)}static meridiems({locale:t=null}={}){return R.create(t).meridiems()}static eras(t="short",{locale:e=null}={}){return R.create(e,null,"gregory").eras(t)}static features(){return{relative:gn(),localeWeek:pn()}}};function Ic(s,t){let e=n=>n.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),i=e(t)-e(s);return Math.floor(G.fromMillis(i).as("days"))}function Cg(s,t,e){let i=[["years",(l,c)=>c.year-l.year],["quarters",(l,c)=>c.quarter-l.quarter+(c.year-l.year)*4],["months",(l,c)=>c.month-l.month+(c.year-l.year)*12],["weeks",(l,c)=>{let h=Ic(l,c);return(h-h%7)/7}],["days",Ic]],n={},o=s,r,a;for(let[l,c]of i)e.indexOf(l)>=0&&(r=l,n[l]=c(s,t),a=o.plus(n),a>t?(n[l]--,s=o.plus(n),s>t&&(a=s,n[l]--,s=o.plus(n))):s=a);return[s,n,a,r]}function Cc(s,t,e,i){let[n,o,r,a]=Cg(s,t,e),l=t-n,c=e.filter(u=>["hours","minutes","seconds","milliseconds"].indexOf(u)>=0);c.length===0&&(r<t&&(r=n.plus({[a]:1})),r!==n&&(o[a]=(o[a]||0)+l/(r-n)));let h=G.fromObject(o,i);return c.length>0?G.fromMillis(l,i).shiftTo(...c).plus(h):h}var Fg="missing Intl.DateTimeFormat.formatToParts support";function W(s,t=e=>e){return{regex:s,deser:([e])=>t(Bl(e))}}var Ag="\xA0",Lc=`[ ${Ag}]`,Pc=new RegExp(Lc,"g");function Lg(s){return s.replace(/\./g,"\\.?").replace(Pc,Lc)}function Fc(s){return s.replace(/\./g,"").replace(Pc," ").toLowerCase()}function At(s,t){return s===null?null:{regex:RegExp(s.map(Lg).join("|")),deser:([e])=>s.findIndex(i=>Fc(e)===Fc(i))+t}}function Ac(s,t){return{regex:s,deser:([,e,i])=>Ie(e,i),groups:t}}function _n(s){return{regex:s,deser:([t])=>t}}function Pg(s){return s.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function Ng(s,t){let e=kt(t),i=kt(t,"{2}"),n=kt(t,"{3}"),o=kt(t,"{4}"),r=kt(t,"{6}"),a=kt(t,"{1,2}"),l=kt(t,"{1,3}"),c=kt(t,"{1,6}"),h=kt(t,"{1,9}"),u=kt(t,"{2,4}"),d=kt(t,"{4,6}"),f=p=>({regex:RegExp(Pg(p.val)),deser:([y])=>y,literal:!0}),g=(p=>{if(s.literal)return f(p);switch(p.val){case"G":return At(t.eras("short"),0);case"GG":return At(t.eras("long"),0);case"y":return W(c);case"yy":return W(u,bi);case"yyyy":return W(o);case"yyyyy":return W(d);case"yyyyyy":return W(r);case"M":return W(a);case"MM":return W(i);case"MMM":return At(t.months("short",!0),1);case"MMMM":return At(t.months("long",!0),1);case"L":return W(a);case"LL":return W(i);case"LLL":return At(t.months("short",!1),1);case"LLLL":return At(t.months("long",!1),1);case"d":return W(a);case"dd":return W(i);case"o":return W(l);case"ooo":return W(n);case"HH":return W(i);case"H":return W(a);case"hh":return W(i);case"h":return W(a);case"mm":return W(i);case"m":return W(a);case"q":return W(a);case"qq":return W(i);case"s":return W(a);case"ss":return W(i);case"S":return W(l);case"SSS":return W(n);case"u":return _n(h);case"uu":return _n(a);case"uuu":return W(e);case"a":return At(t.meridiems(),0);case"kkkk":return W(o);case"kk":return W(u,bi);case"W":return W(a);case"WW":return W(i);case"E":case"c":return W(e);case"EEE":return At(t.weekdays("short",!1),1);case"EEEE":return At(t.weekdays("long",!1),1);case"ccc":return At(t.weekdays("short",!0),1);case"cccc":return At(t.weekdays("long",!0),1);case"Z":case"ZZ":return Ac(new RegExp(`([+-]${a.source})(?::(${i.source}))?`),2);case"ZZZ":return Ac(new RegExp(`([+-]${a.source})(${i.source})?`),2);case"z":return _n(/[a-z_+-/]{1,256}?/i);case" ":return _n(/[^\S\n\r]/);default:return f(p)}})(s)||{invalidReason:Fg};return g.token=s,g}var Rg={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function Wg(s,t,e){let{type:i,value:n}=s;if(i==="literal"){let l=/^\s+$/.test(n);return{literal:!l,val:l?" ":n}}let o=t[i],r=i;i==="hour"&&(t.hour12!=null?r=t.hour12?"hour12":"hour24":t.hourCycle!=null?t.hourCycle==="h11"||t.hourCycle==="h12"?r="hour12":r="hour24":r=e.hour12?"hour12":"hour24");let a=Rg[r];if(typeof a=="object"&&(a=a[o]),a)return{literal:!1,val:a}}function zg(s){return[`^${s.map(e=>e.regex).reduce((e,i)=>`${e}(${i.source})`,"")}$`,s]}function Vg(s,t,e){let i=s.match(t);if(i){let n={},o=1;for(let r in e)if(fe(e,r)){let a=e[r],l=a.groups?a.groups+1:1;!a.literal&&a.token&&(n[a.token.val[0]]=a.deser(i.slice(o,o+l))),o+=l}return[i,n]}else return[i,{}]}function Hg(s){let t=o=>{switch(o){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},e=null,i;return D(s.z)||(e=ct.create(s.z)),D(s.Z)||(e||(e=new et(s.Z)),i=s.Z),D(s.q)||(s.M=(s.q-1)*3+1),D(s.h)||(s.h<12&&s.a===1?s.h+=12:s.h===12&&s.a===0&&(s.h=0)),s.G===0&&s.y&&(s.y=-s.y),D(s.u)||(s.S=yi(s.u)),[Object.keys(s).reduce((o,r)=>{let a=t(r);return a&&(o[a]=s[r]),o},{}),e,i]}var gr=null;function Bg(){return gr||(gr=F.fromMillis(1555555555555)),gr}function $g(s,t){if(s.literal)return s;let e=it.macroTokenToFormatOpts(s.val),i=br(e,t);return i==null||i.includes(void 0)?s:i}function pr(s,t){return Array.prototype.concat(...s.map(e=>$g(e,t)))}var wi=class{constructor(t,e){if(this.locale=t,this.format=e,this.tokens=pr(it.parseFormat(e),t),this.units=this.tokens.map(i=>Ng(i,t)),this.disqualifyingUnit=this.units.find(i=>i.invalidReason),!this.disqualifyingUnit){let[i,n]=zg(this.units);this.regex=RegExp(i,"i"),this.handlers=n}}explainFromTokens(t){if(this.isValid){let[e,i]=Vg(t,this.regex,this.handlers),[n,o,r]=i?Hg(i):[null,null,void 0];if(fe(i,"a")&&fe(i,"H"))throw new Dt("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:this.tokens,regex:this.regex,rawMatches:e,matches:i,result:n,zone:o,specificOffset:r}}else return{input:t,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}};function yr(s,t,e){return new wi(s,e).explainFromTokens(t)}function Nc(s,t,e){let{result:i,zone:n,specificOffset:o,invalidReason:r}=yr(s,t,e);return[i,n,o,r]}function br(s,t){if(!s)return null;let i=it.create(t,s).dtFormatter(Bg()),n=i.formatToParts(),o=i.resolvedOptions();return n.map(r=>Wg(r,s,o))}var xr="Invalid DateTime",Rc=864e13;function Si(s){return new st("unsupported zone",`the zone "${s.name}" is not supported`)}function _r(s){return s.weekData===null&&(s.weekData=mi(s.c)),s.weekData}function wr(s){return s.localWeekData===null&&(s.localWeekData=mi(s.c,s.loc.getMinDaysInFirstWeek(),s.loc.getStartOfWeek())),s.localWeekData}function Le(s,t){let e={ts:s.ts,zone:s.zone,c:s.c,o:s.o,loc:s.loc,invalid:s.invalid};return new F({...e,...t,old:e})}function jc(s,t,e){let i=s-t*60*1e3,n=e.offset(i);if(t===n)return[i,t];i-=(n-t)*60*1e3;let o=e.offset(i);return n===o?[i,n]:[s-Math.min(n,o)*60*1e3,Math.max(n,o)]}function wn(s,t){s+=t*60*1e3;let e=new Date(s);return{year:e.getUTCFullYear(),month:e.getUTCMonth()+1,day:e.getUTCDate(),hour:e.getUTCHours(),minute:e.getUTCMinutes(),second:e.getUTCSeconds(),millisecond:e.getUTCMilliseconds()}}function kn(s,t,e){return jc(os(s),t,e)}function Wc(s,t){let e=s.o,i=s.c.year+Math.trunc(t.years),n=s.c.month+Math.trunc(t.months)+Math.trunc(t.quarters)*3,o={...s.c,year:i,month:n,day:Math.min(s.c.day,ls(i,n))+Math.trunc(t.days)+Math.trunc(t.weeks)*7},r=G.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),a=os(o),[l,c]=jc(a,e,s.zone);return r!==0&&(l+=r,c=s.zone.offset(l)),{ts:l,o:c}}function bs(s,t,e,i,n,o){let{setZone:r,zone:a}=e;if(s&&Object.keys(s).length!==0||t){let l=t||a,c=F.fromObject(s,{...e,zone:l,specificOffset:o});return r?c:c.setZone(a)}else return F.invalid(new st("unparsable",`the input "${n}" can't be parsed as ${i}`))}function Sn(s,t,e=!0){return s.isValid?it.create(R.create("en-US"),{allowZ:e,forceSimple:!0}).formatDateTimeFromString(s,t):null}function Sr(s,t){let e=s.c.year>9999||s.c.year<0,i="";return e&&s.c.year>=0&&(i+="+"),i+=q(s.c.year,e?6:4),t?(i+="-",i+=q(s.c.month),i+="-",i+=q(s.c.day)):(i+=q(s.c.month),i+=q(s.c.day)),i}function zc(s,t,e,i,n,o){let r=q(s.c.hour);return t?(r+=":",r+=q(s.c.minute),(s.c.millisecond!==0||s.c.second!==0||!e)&&(r+=":")):r+=q(s.c.minute),(s.c.millisecond!==0||s.c.second!==0||!e)&&(r+=q(s.c.second),(s.c.millisecond!==0||!i)&&(r+=".",r+=q(s.c.millisecond,3))),n&&(s.isOffsetFixed&&s.offset===0&&!o?r+="Z":s.o<0?(r+="-",r+=q(Math.trunc(-s.o/60)),r+=":",r+=q(Math.trunc(-s.o%60))):(r+="+",r+=q(Math.trunc(s.o/60)),r+=":",r+=q(Math.trunc(s.o%60)))),o&&(r+="["+s.zone.ianaName+"]"),r}var Uc={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},jg={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Ug={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Yc=["year","month","day","hour","minute","second","millisecond"],Yg=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Zg=["year","ordinal","hour","minute","second","millisecond"];function qg(s){let t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[s.toLowerCase()];if(!t)throw new ns(s);return t}function Vc(s){switch(s.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return qg(s)}}function Gg(s){if(ki===void 0&&(ki=N.now()),s.type!=="iana")return s.offset(ki);let t=s.name,e=kr.get(t);return e===void 0&&(e=s.offset(ki),kr.set(t,e)),e}function Hc(s,t){let e=Ct(t.zone,N.defaultZone);if(!e.isValid)return F.invalid(Si(e));let i=R.fromObject(t),n,o;if(D(s.year))n=N.now();else{for(let l of Yc)D(s[l])&&(s[l]=Uc[l]);let r=or(s)||rr(s);if(r)return F.invalid(r);let a=Gg(e);[n,o]=kn(s,a,e)}return new F({ts:n,zone:e,loc:i,o})}function Bc(s,t,e){let i=D(e.round)?!0:e.round,n=(r,a)=>(r=rs(r,i||e.calendary?0:2,!0),t.loc.clone(e).relFormatter(e).format(r,a)),o=r=>e.calendary?t.hasSame(s,r)?0:t.startOf(r).diff(s.startOf(r),r).get(r):t.diff(s,r).get(r);if(e.unit)return n(o(e.unit),e.unit);for(let r of e.units){let a=o(r);if(Math.abs(a)>=1)return n(a,r)}return n(s>t?-0:0,e.units[e.units.length-1])}function $c(s){let t={},e;return s.length>0&&typeof s[s.length-1]=="object"?(t=s[s.length-1],e=Array.from(s).slice(0,s.length-1)):e=Array.from(s),[t,e]}var ki,kr=new Map,F=class s{constructor(t){let e=t.zone||N.defaultZone,i=t.invalid||(Number.isNaN(t.ts)?new st("invalid input"):null)||(e.isValid?null:Si(e));this.ts=D(t.ts)?N.now():t.ts;let n=null,o=null;if(!i)if(t.old&&t.old.ts===this.ts&&t.old.zone.equals(e))[n,o]=[t.old.c,t.old.o];else{let a=Ft(t.o)&&!t.old?t.o:e.offset(this.ts);n=wn(this.ts,a),i=Number.isNaN(n.year)?new st("invalid input"):null,n=i?null:n,o=i?null:a}this._zone=e,this.loc=t.loc||R.create(),this.invalid=i,this.weekData=null,this.localWeekData=null,this.c=n,this.o=o,this.isLuxonDateTime=!0}static now(){return new s({})}static local(){let[t,e]=$c(arguments),[i,n,o,r,a,l,c]=e;return Hc({year:i,month:n,day:o,hour:r,minute:a,second:l,millisecond:c},t)}static utc(){let[t,e]=$c(arguments),[i,n,o,r,a,l,c]=e;return t.zone=et.utcInstance,Hc({year:i,month:n,day:o,hour:r,minute:a,second:l,millisecond:c},t)}static fromJSDate(t,e={}){let i=oc(t)?t.valueOf():NaN;if(Number.isNaN(i))return s.invalid("invalid input");let n=Ct(e.zone,N.defaultZone);return n.isValid?new s({ts:i,zone:n,loc:R.fromObject(e)}):s.invalid(Si(n))}static fromMillis(t,e={}){if(Ft(t))return t<-Rc||t>Rc?s.invalid("Timestamp out of range"):new s({ts:t,zone:Ct(e.zone,N.defaultZone),loc:R.fromObject(e)});throw new K(`fromMillis requires a numerical input, but received a ${typeof t} with value ${t}`)}static fromSeconds(t,e={}){if(Ft(t))return new s({ts:t*1e3,zone:Ct(e.zone,N.defaultZone),loc:R.fromObject(e)});throw new K("fromSeconds requires a numerical input")}static fromObject(t,e={}){t=t||{};let i=Ct(e.zone,N.defaultZone);if(!i.isValid)return s.invalid(Si(i));let n=R.fromObject(e),o=cs(t,Vc),{minDaysInFirstWeek:r,startOfWeek:a}=nr(o,n),l=N.now(),c=D(e.specificOffset)?i.offset(l):e.specificOffset,h=!D(o.ordinal),u=!D(o.year),d=!D(o.month)||!D(o.day),f=u||d,m=o.weekYear||o.weekNumber;if((f||h)&&m)throw new Dt("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(d&&h)throw new Dt("Can't mix ordinal dates with month/day");let g=m||o.weekday&&!f,p,y,b=wn(l,c);g?(p=Yg,y=jg,b=mi(b,r,a)):h?(p=Zg,y=Ug,b=xn(b)):(p=Yc,y=Uc);let _=!1;for(let E of p){let C=o[E];D(C)?_?o[E]=y[E]:o[E]=b[E]:_=!0}let w=g?sc(o,r,a):h?ic(o):or(o),x=w||rr(o);if(x)return s.invalid(x);let k=g?sr(o,r,a):h?ir(o):o,[M,v]=kn(k,c,i),O=new s({ts:M,zone:i,o:v,loc:n});return o.weekday&&f&&t.weekday!==O.weekday?s.invalid("mismatched weekday",`you can't specify both a weekday of ${o.weekday} and a date of ${O.toISO()}`):O.isValid?O:s.invalid(O.invalid)}static fromISO(t,e={}){let[i,n]=_c(t);return bs(i,n,e,"ISO 8601",t)}static fromRFC2822(t,e={}){let[i,n]=wc(t);return bs(i,n,e,"RFC 2822",t)}static fromHTTP(t,e={}){let[i,n]=Sc(t);return bs(i,n,e,"HTTP",e)}static fromFormat(t,e,i={}){if(D(t)||D(e))throw new K("fromFormat requires an input string and a format");let{locale:n=null,numberingSystem:o=null}=i,r=R.fromOpts({locale:n,numberingSystem:o,defaultToEN:!0}),[a,l,c,h]=Nc(r,t,e);return h?s.invalid(h):bs(a,l,i,`format ${e}`,t,c)}static fromString(t,e,i={}){return s.fromFormat(t,e,i)}static fromSQL(t,e={}){let[i,n]=vc(t);return bs(i,n,e,"SQL",t)}static invalid(t,e=null){if(!t)throw new K("need to specify a reason the DateTime is invalid");let i=t instanceof st?t:new st(t,e);if(N.throwOnInvalid)throw new cn(i);return new s({invalid:i})}static isDateTime(t){return t&&t.isLuxonDateTime||!1}static parseFormatForOpts(t,e={}){let i=br(t,R.fromObject(e));return i?i.map(n=>n?n.val:null).join(""):null}static expandFormat(t,e={}){return pr(it.parseFormat(t),R.fromObject(e)).map(n=>n.val).join("")}static resetCache(){ki=void 0,kr.clear()}get(t){return this[t]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?_r(this).weekYear:NaN}get weekNumber(){return this.isValid?_r(this).weekNumber:NaN}get weekday(){return this.isValid?_r(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?wr(this).weekday:NaN}get localWeekNumber(){return this.isValid?wr(this).weekNumber:NaN}get localWeekYear(){return this.isValid?wr(this).weekYear:NaN}get ordinal(){return this.isValid?xn(this.c).ordinal:NaN}get monthShort(){return this.isValid?Qt.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?Qt.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?Qt.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?Qt.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let t=864e5,e=6e4,i=os(this.c),n=this.zone.offset(i-t),o=this.zone.offset(i+t),r=this.zone.offset(i-n*e),a=this.zone.offset(i-o*e);if(r===a)return[this];let l=i-r*e,c=i-a*e,h=wn(l,r),u=wn(c,a);return h.hour===u.hour&&h.minute===u.minute&&h.second===u.second&&h.millisecond===u.millisecond?[Le(this,{ts:l}),Le(this,{ts:c})]:[this]}get isInLeapYear(){return Fe(this.year)}get daysInMonth(){return ls(this.year,this.month)}get daysInYear(){return this.isValid?de(this.year):NaN}get weeksInWeekYear(){return this.isValid?Ce(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?Ce(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(t={}){let{locale:e,numberingSystem:i,calendar:n}=it.create(this.loc.clone(t),t).resolvedOptions(this);return{locale:e,numberingSystem:i,outputCalendar:n}}toUTC(t=0,e={}){return this.setZone(et.instance(t),e)}toLocal(){return this.setZone(N.defaultZone)}setZone(t,{keepLocalTime:e=!1,keepCalendarTime:i=!1}={}){if(t=Ct(t,N.defaultZone),t.equals(this.zone))return this;if(t.isValid){let n=this.ts;if(e||i){let o=t.offset(this.ts),r=this.toObject();[n]=kn(r,o,t)}return Le(this,{ts:n,zone:t})}else return s.invalid(Si(t))}reconfigure({locale:t,numberingSystem:e,outputCalendar:i}={}){let n=this.loc.clone({locale:t,numberingSystem:e,outputCalendar:i});return Le(this,{loc:n})}setLocale(t){return this.reconfigure({locale:t})}set(t){if(!this.isValid)return this;let e=cs(t,Vc),{minDaysInFirstWeek:i,startOfWeek:n}=nr(e,this.loc),o=!D(e.weekYear)||!D(e.weekNumber)||!D(e.weekday),r=!D(e.ordinal),a=!D(e.year),l=!D(e.month)||!D(e.day),c=a||l,h=e.weekYear||e.weekNumber;if((c||r)&&h)throw new Dt("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(l&&r)throw new Dt("Can't mix ordinal dates with month/day");let u;o?u=sr({...mi(this.c,i,n),...e},i,n):D(e.ordinal)?(u={...this.toObject(),...e},D(e.day)&&(u.day=Math.min(ls(u.year,u.month),u.day))):u=ir({...xn(this.c),...e});let[d,f]=kn(u,this.o,this.zone);return Le(this,{ts:d,o:f})}plus(t){if(!this.isValid)return this;let e=G.fromDurationLike(t);return Le(this,Wc(this,e))}minus(t){if(!this.isValid)return this;let e=G.fromDurationLike(t).negate();return Le(this,Wc(this,e))}startOf(t,{useLocaleWeeks:e=!1}={}){if(!this.isValid)return this;let i={},n=G.normalizeUnit(t);switch(n){case"years":i.month=1;case"quarters":case"months":i.day=1;case"weeks":case"days":i.hour=0;case"hours":i.minute=0;case"minutes":i.second=0;case"seconds":i.millisecond=0;break;case"milliseconds":break}if(n==="weeks")if(e){let o=this.loc.getStartOfWeek(),{weekday:r}=this;r<o&&(i.weekNumber=this.weekNumber-1),i.weekday=o}else i.weekday=1;if(n==="quarters"){let o=Math.ceil(this.month/3);i.month=(o-1)*3+1}return this.set(i)}endOf(t,e){return this.isValid?this.plus({[t]:1}).startOf(t,e).minus(1):this}toFormat(t,e={}){return this.isValid?it.create(this.loc.redefaultToEN(e)).formatDateTimeFromString(this,t):xr}toLocaleString(t=ce,e={}){return this.isValid?it.create(this.loc.clone(e),t).formatDateTime(this):xr}toLocaleParts(t={}){return this.isValid?it.create(this.loc.clone(t),t).formatDateTimeParts(this):[]}toISO({format:t="extended",suppressSeconds:e=!1,suppressMilliseconds:i=!1,includeOffset:n=!0,extendedZone:o=!1}={}){if(!this.isValid)return null;let r=t==="extended",a=Sr(this,r);return a+="T",a+=zc(this,r,e,i,n,o),a}toISODate({format:t="extended"}={}){return this.isValid?Sr(this,t==="extended"):null}toISOWeekDate(){return Sn(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:t=!1,suppressSeconds:e=!1,includeOffset:i=!0,includePrefix:n=!1,extendedZone:o=!1,format:r="extended"}={}){return this.isValid?(n?"T":"")+zc(this,r==="extended",e,t,i,o):null}toRFC2822(){return Sn(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Sn(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Sr(this,!0):null}toSQLTime({includeOffset:t=!0,includeZone:e=!1,includeOffsetSpace:i=!0}={}){let n="HH:mm:ss.SSS";return(e||t)&&(i&&(n+=" "),e?n+="z":t&&(n+="ZZ")),Sn(this,n,!0)}toSQL(t={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(t)}`:null}toString(){return this.isValid?this.toISO():xr}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(t={}){if(!this.isValid)return{};let e={...this.c};return t.includeConfig&&(e.outputCalendar=this.outputCalendar,e.numberingSystem=this.loc.numberingSystem,e.locale=this.loc.locale),e}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(t,e="milliseconds",i={}){if(!this.isValid||!t.isValid)return G.invalid("created by diffing an invalid DateTime");let n={locale:this.locale,numberingSystem:this.numberingSystem,...i},o=rc(e).map(G.normalizeUnit),r=t.valueOf()>this.valueOf(),a=r?this:t,l=r?t:this,c=Cc(a,l,o,n);return r?c.negate():c}diffNow(t="milliseconds",e={}){return this.diff(s.now(),t,e)}until(t){return this.isValid?Jt.fromDateTimes(this,t):this}hasSame(t,e,i){if(!this.isValid)return!1;let n=t.valueOf(),o=this.setZone(t.zone,{keepLocalTime:!0});return o.startOf(e,i)<=n&&n<=o.endOf(e,i)}equals(t){return this.isValid&&t.isValid&&this.valueOf()===t.valueOf()&&this.zone.equals(t.zone)&&this.loc.equals(t.loc)}toRelative(t={}){if(!this.isValid)return null;let e=t.base||s.fromObject({},{zone:this.zone}),i=t.padding?this<e?-t.padding:t.padding:0,n=["years","months","days","hours","minutes","seconds"],o=t.unit;return Array.isArray(t.unit)&&(n=t.unit,o=void 0),Bc(e,this.plus(i),{...t,numeric:"always",units:n,unit:o})}toRelativeCalendar(t={}){return this.isValid?Bc(t.base||s.fromObject({},{zone:this.zone}),this,{...t,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...t){if(!t.every(s.isDateTime))throw new K("min requires all arguments be DateTimes");return ar(t,e=>e.valueOf(),Math.min)}static max(...t){if(!t.every(s.isDateTime))throw new K("max requires all arguments be DateTimes");return ar(t,e=>e.valueOf(),Math.max)}static fromFormatExplain(t,e,i={}){let{locale:n=null,numberingSystem:o=null}=i,r=R.fromOpts({locale:n,numberingSystem:o,defaultToEN:!0});return yr(r,t,e)}static fromStringExplain(t,e,i={}){return s.fromFormatExplain(t,e,i)}static buildFormatParser(t,e={}){let{locale:i=null,numberingSystem:n=null}=e,o=R.fromOpts({locale:i,numberingSystem:n,defaultToEN:!0});return new wi(o,t)}static fromFormatParser(t,e,i={}){if(D(t)||D(e))throw new K("fromFormatParser requires an input string and a format parser");let{locale:n=null,numberingSystem:o=null}=i,r=R.fromOpts({locale:n,numberingSystem:o,defaultToEN:!0});if(!r.equals(e.locale))throw new K(`fromFormatParser called with a locale of ${r}, but the format parser was created for ${e.locale}`);let{result:a,zone:l,specificOffset:c,invalidReason:h}=e.explainFromTokens(t);return h?s.invalid(h):bs(a,l,i,`format ${e.format}`,t,c)}static get DATE_SHORT(){return ce}static get DATE_MED(){return Zs}static get DATE_MED_WITH_WEEKDAY(){return Lo}static get DATE_FULL(){return qs}static get DATE_HUGE(){return Gs}static get TIME_SIMPLE(){return Xs}static get TIME_WITH_SECONDS(){return Ks}static get TIME_WITH_SHORT_OFFSET(){return Js}static get TIME_WITH_LONG_OFFSET(){return Qs}static get TIME_24_SIMPLE(){return ti}static get TIME_24_WITH_SECONDS(){return ei}static get TIME_24_WITH_SHORT_OFFSET(){return si}static get TIME_24_WITH_LONG_OFFSET(){return ii}static get DATETIME_SHORT(){return ni}static get DATETIME_SHORT_WITH_SECONDS(){return oi}static get DATETIME_MED(){return ri}static get DATETIME_MED_WITH_SECONDS(){return ai}static get DATETIME_MED_WITH_WEEKDAY(){return Po}static get DATETIME_FULL(){return li}static get DATETIME_FULL_WITH_SECONDS(){return ci}static get DATETIME_HUGE(){return hi}static get DATETIME_HUGE_WITH_SECONDS(){return ui}};function ys(s){if(F.isDateTime(s))return s;if(s&&s.valueOf&&Ft(s.valueOf()))return F.fromJSDate(s);if(s&&typeof s=="object")return F.fromObject(s);throw new K(`Unknown datetime argument: ${s}, of type ${typeof s}`)}var Xg={datetime:F.DATETIME_MED_WITH_SECONDS,millisecond:"h:mm:ss.SSS a",second:F.TIME_WITH_SECONDS,minute:F.TIME_SIMPLE,hour:{hour:"numeric"},day:{day:"numeric",month:"short"},week:"DD",month:{month:"short",year:"numeric"},quarter:"'Q'q - yyyy",year:{year:"numeric"}};Fo._date.override({_id:"luxon",_create:function(s){return F.fromMillis(s,this.options)},init(s){this.options.locale||(this.options.locale=s.locale)},formats:function(){return Xg},parse:function(s,t){let e=this.options,i=typeof s;return s===null||i==="undefined"?null:(i==="number"?s=this._create(s):i==="string"?typeof t=="string"?s=F.fromFormat(s,t,e):s=F.fromISO(s,e):s instanceof Date?s=F.fromJSDate(s,e):i==="object"&&!(s instanceof F)&&(s=F.fromObject(s,e)),s.isValid?s.valueOf():null)},format:function(s,t){let e=this._create(s);return typeof t=="string"?e.toFormat(t):e.toLocaleString(t)},add:function(s,t,e){let i={};return i[e]=t,this._create(s).plus(i).valueOf()},diff:function(s,t,e){return this._create(s).diff(this._create(t)).as(e).valueOf()},startOf:function(s,t,e){if(t==="isoWeek"){e=Math.trunc(Math.min(Math.max(0,e),6));let i=this._create(s);return i.minus({days:(i.weekday-e+7)%7}).startOf("day").valueOf()}return t?this._create(s).startOf(t).valueOf():s},endOf:function(s,t){return this._create(s).endOf(t).valueOf()}});function Mn({cachedData:s,options:t,type:e}){return{init(){this.initChart(),this.$wire.$on("updateChartData",({data:i})=>{Mn=this.getChart(),Mn.data=i,Mn.update("resize")}),Alpine.effect(()=>{Alpine.store("theme"),this.$nextTick(()=>{this.getChart()&&(this.getChart().destroy(),this.initChart())})}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{Alpine.store("theme")==="system"&&this.$nextTick(()=>{this.getChart().destroy(),this.initChart()})})},initChart(i=null){var r,a,l,c,h,u,d,f,m,g,p,y,b,_;if(!this.$refs.canvas||!this.$refs.backgroundColorElement||!this.$refs.borderColorElement||!this.$refs.textColorElement||!this.$refs.gridColorElement)return;Ht.defaults.animation.duration=0,Ht.defaults.backgroundColor=getComputedStyle(this.$refs.backgroundColorElement).color;let n=getComputedStyle(this.$refs.borderColorElement).color;Ht.defaults.borderColor=n,Ht.defaults.color=getComputedStyle(this.$refs.textColorElement).color,Ht.defaults.font.family=getComputedStyle(this.$el).fontFamily,Ht.defaults.plugins.legend.labels.boxWidth=12,Ht.defaults.plugins.legend.position="bottom";let o=getComputedStyle(this.$refs.gridColorElement).color;return t??(t={}),t.borderWidth??(t.borderWidth=2),t.pointBackgroundColor??(t.pointBackgroundColor=n),t.pointHitRadius??(t.pointHitRadius=4),t.pointRadius??(t.pointRadius=2),t.scales??(t.scales={}),(r=t.scales).x??(r.x={}),(a=t.scales.x).border??(a.border={}),(l=t.scales.x.border).display??(l.display=!1),(c=t.scales.x).grid??(c.grid={}),(h=t.scales.x.grid).color??(h.color=o),(u=t.scales.x.grid).display??(u.display=!1),(d=t.scales).y??(d.y={}),(f=t.scales.y).border??(f.border={}),(m=t.scales.y.border).display??(m.display=!1),(g=t.scales.y).grid??(g.grid={}),(p=t.scales.y.grid).color??(p.color=o),["doughnut","pie"].includes(e)&&((y=t.scales.x).display??(y.display=!1),(b=t.scales.y).display??(b.display=!1),(_=t.scales.y.grid).display??(_.display=!1)),new Ht(this.$refs.canvas,{type:e,data:i??s,options:t,plugins:window.filamentChartJsPlugins??[]})},getChart(){return this.$refs.canvas?Ht.getChart(this.$refs.canvas):null}}}export{Mn as default};
/*! Bundled license information:

@kurkle/color/dist/color.esm.js:
  (*!
   * @kurkle/color v0.3.4
   * https://github.com/kurkle/color#readme
   * (c) 2024 Jukka Kurkela
   * Released under the MIT License
   *)

chart.js/dist/chunks/helpers.dataset.js:
chart.js/dist/chart.js:
  (*!
   * Chart.js v4.4.9
   * https://www.chartjs.org
   * (c) 2025 Chart.js Contributors
   * Released under the MIT License
   *)

chartjs-adapter-luxon/dist/chartjs-adapter-luxon.esm.js:
  (*!
   * chartjs-adapter-luxon v1.3.1
   * https://www.chartjs.org
   * (c) 2023 chartjs-adapter-luxon Contributors
   * Released under the MIT license
   *)
*/
