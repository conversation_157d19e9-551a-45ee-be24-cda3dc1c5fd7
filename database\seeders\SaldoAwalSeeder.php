<?php

namespace Database\Seeders;

use App\Models\Coa;
use App\Models\Saldo;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class SaldoAwalSeeder extends Seeder
{
    public function run()
    {
        Saldo::truncate();

        $coas = Coa::where('tipe', 'detail')->get();
        $periode = Carbon::now()->startOfMonth();

        foreach ($coas as $coa) {
            // Hanya set saldo awal untuk akun tertentu
            if (in_array($coa->kode, ['1.1', '3.1'])) {
                Saldo::create([
                    'coa_id' => $coa->id,
                    'periode' => $periode,
                    'saldo_awal' => $coa->kode === '1.1' ? 5000000 : 5000000,
                    'saldo_akhir' => $coa->kode === '1.1' ? 5000000 : 5000000,
                ]);
            } else {
                Saldo::create([
                    'coa_id' => $coa->id,
                    'periode' => $periode,
                    'saldo_awal' => 0,
                    'saldo_akhir' => 0,
                ]);
            }
        }
    }
}
