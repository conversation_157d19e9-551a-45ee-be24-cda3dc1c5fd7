<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Keluarga extends Model
{
    use HasFactory;

    protected $fillable = [
        'no_kk', 'kepala_keluarga_id', 'alamat', 'rt', 'rw'
    ];

    // Relasi ke Warga (kepala keluarga)
    public function kepalaKeluarga()
    {
        return $this->belongsTo(Warga::class, 'kepala_keluarga_id');
    }

    // Relasi ke Anggota Keluarga
    public function anggotaKeluarga(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Warga::class)
            ->where('id', '!=', $this->kepala_keluarga_id);
    }

    // Relasi ke Iuran
    public function iuran()
    {
        return $this->hasMany(IuranWarga::class);
    }

    // Query scope untuk filter RT
    public function scopeRt($query, $rt)
    {
        return $query->where('rt', $rt);
    }
}
