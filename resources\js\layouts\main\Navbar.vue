<script setup lang="ts">
import { ref, watch } from 'vue'
import { Link, usePage } from '@inertiajs/vue3'
import type { SharedData, User } from '@/types'
import PengaturanDrawer from '@/components/PengaturanDrawer.vue'


const page = usePage<SharedData>()
const openDrawer = ref(false)

watch(openDrawer, (val) => {
    if (val) {
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth
        document.body.style.overflow = 'hidden'
        document.body.style.paddingRight = `${scrollbarWidth}px`
    } else {
        document.body.style.overflow = ''
        document.body.style.paddingRight = ''
    }
})

const menuItems = [
    { to: "/", label: "Beranda", icon: 'solar:home-smile-bold-duotone' },
    { to: "/#", label: "Buat Laporan", icon: 'solar:add-square-bold-duotone' },
    { to: "pengaturan", label: "Pengaturan", icon: 'solar:settings-minimalistic-bold-duotone' },
]

interface Props {
    user: User
}
defineProps<Props>()
</script>

<template>
    <nav
        class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-xl bg-white/30 backdrop-blur-lg dark:bg-black/20 rounded-2xl shadow-lg z-10 border-t border-t-white/30 dark:border-t-black/20"
    >
        <ul class="flex justify-around items-center pb-2">
            <li
                v-for="item in menuItems"
                :key="item.label"
            >
                <!-- Ganti Pengaturan dengan tombol yang memicu drawer -->
                <button
                    v-if="item.label === 'Pengaturan'"
                    @click="openDrawer = true"
                    class="size-18 rounded-b-xl flex items-center justify-center flex-col text-gray-600 hover:text-blue-500 relative pt-1 px-10"
                    :class="{'bg-gradient-to-b from-blue-400/20 to-transparent dark:from-blue-400/30 !text-blue-6D00 dark:!text-blue-200': openDrawer}"
                >
                    <div
                        v-if="openDrawer"
                        class="absolute top-0 border-t-4 border-t-blue-500 w-full rounded-b-3xl"
                    ></div>
                    <Icon :icon="item.icon" class="size-8" />
                    <span class="text-xs mt-1 text-nowrap">{{ item.label }}</span>
                </button>

                <Link
                    v-else
                    :href="item.to"
                    :method="item.label === 'Logout' ? 'post' : 'get'"
                    class="size-18 rounded-b-xl flex items-center justify-center flex-col text-gray-600 hover:text-blue-500 relative pt-1 px-10"
                    :class="{'bg-gradient-to-b from-blue-400/20 to-transparent dark:from-blue-400/30 !text-blue-6D00 dark:!text-blue-200': page.url === item.to}"
                >
                    <div
                        v-if="page.url === item.to"
                        class="absolute top-0 border-t-4 border-t-blue-500 w-full rounded-b-3xl"
                    ></div>
                    <Icon :icon="item.icon" class="size-8" />
                    <span class="text-xs mt-1 text-nowrap">{{ item.label }}</span>
                </Link>
            </li>
        </ul>
        <!-- Drawer dipanggil di luar list -->
        <PengaturanDrawer v-model="openDrawer" :open="openDrawer" />
    </nav>
</template>
