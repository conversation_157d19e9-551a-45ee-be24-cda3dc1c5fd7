#!/bin/sh

echo "Setting up Laravel directories..."

# Pastikan direktori ada dengan permission yang benar
mkdir -p /rtapp/storage/logs \
         /rtapp/storage/framework/sessions \
         /rtapp/storage/framework/views \
         /rtapp/storage/framework/cache \
         /rtapp/bootstrap/cache

# Set permission (hanya jika running sebagai root, otherwise skip)
if [ "$(id -u)" = "0" ]; then
    chown -R www-data:www-data /rtapp/storage /rtapp/bootstrap/cache
    chmod -R 775 /rtapp/storage /rtapp/bootstrap/cache
fi

echo "Waiting for services to be ready..."

# Wait for Redis (if using Redis)
if grep -q "REDIS_HOST=redis" .env.local 2>/dev/null; then
    echo "Waiting for Redis..."
    until nc -z redis 6379; do
        echo "Redis is unavailable - sleeping"
        sleep 2
    done
    echo "Redis is ready!"
fi

# Wait for PostgreSQL
echo "Waiting for PostgreSQL..."
until nc -z postgres 5432; do
    echo "PostgreSQL is unavailable - sleeping"
    sleep 2
done
echo "PostgreSQL is ready!"

echo "Starting Laravel services..."

# Generate key jika belum ada
if [ ! -f ".env" ] || ! grep -q "APP_KEY=" .env || [ -z "$(grep APP_KEY= .env | cut -d= -f2)" ]; then
    echo "Generating application key..."
    php artisan key:generate --force
fi

# Jalankan migrations
echo "Running migrations..."
php artisan migrate --force

# Clear cache (skip jika Redis belum siap)
echo "Clearing cache..."
php artisan config:clear
# Skip cache:clear jika Redis error
php artisan cache:clear 2>/dev/null || echo "Skipping cache:clear (Redis not available)"
php artisan route:clear
php artisan view:clear

php artisan wayfinder:generate

# Jalankan Laravel Horizon di background (jika ada dan Redis tersedia)
if [ -f "artisan" ] && php artisan list | grep -q horizon && nc -z redis 6379 2>/dev/null; then
    echo "Starting Horizon..."
    php artisan horizon &
fi

echo "Starting Laravel Octane with FrankenPHP..."
exec php artisan octane:start --server=frankenphp --host=0.0.0.0 --port=8000
