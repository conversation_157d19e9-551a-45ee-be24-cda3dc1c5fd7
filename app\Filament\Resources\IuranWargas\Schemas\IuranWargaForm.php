<?php

namespace App\Filament\Resources\IuranWargas\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class IuranWargaForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('jenis_iuran_id')
                    ->required()
                    ->numeric(),
                TextInput::make('keluarga_id')
                    ->required()
                    ->numeric(),
                DatePicker::make('tanggal_jatuh_tempo')
                    ->required(),
                Select::make('status')
                    ->options(['belum_lunas' => 'Belum lunas', 'lunas' => 'Lunas', 'tertunggak' => 'Tertunggak'])
                    ->required(),
            ]);
    }
}
