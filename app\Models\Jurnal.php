<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Jurnal extends Model
{
    use HasFactory;

    protected $guarded = [];

    // Relasi ke Transaksi
    public function transaksi()
    {
        return $this->belongsTo(Transaksi::class);
    }

    // Relasi ke COA
    public function coa()
    {
        return $this->belongsTo(Coa::class);
    }

    // Accessor untuk nominal (debit atau kredit)
    public function getNominalAttribute()
    {
        return $this->debit > 0 ? $this->debit : $this->kredit;
    }

    // Accessor untuk jenis (debit/kredit)
    public function getJenisAttribute()
    {
        return $this->debit > 0 ? 'debit' : 'kredit';
    }
}
