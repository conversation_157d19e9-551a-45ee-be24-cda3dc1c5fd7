<?php

namespace Database\Seeders;

use App\Models\KategoriLaporan;
use App\Models\LaporanWarga;
use App\Models\Warga;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class LaporanWargaSeeder extends Seeder
{
    public function run()
    {
        LaporanWarga::truncate();

        $wargas = Warga::all();
        $kategories = KategoriLaporan::all();

        foreach ($wargas->take(30) as $warga) { // Ambil 30 warga acak
            $status = ['diterima', 'diproses', 'selesai', 'ditolak'][rand(0, 3)];

            $laporan = LaporanWarga::create([
                'warga_id' => $warga->id,
                'kategori_id' => $kategories->random()->id,
                'judul' => 'Laporan ' . $kategories->random()->nama . ' ' . rand(1, 100),
                'deskripsi' => 'Ini adalah contoh laporan mengenai ' . $kategories->random()->nama . '. <PERSON><PERSON> ditin<PERSON>ti.',
                'status' => $status,
                'respon' => $status === 'selesai' ? 'Laporan telah ditindaklan<PERSON>ti' : null,
            ]);

            // Jika status bukan diterima, set created_at ke waktu yang lalu
            if ($status !== 'diterima') {
                $laporan->created_at = Carbon::now()->subDays(rand(1, 30));
                $laporan->save();
            }
        }
    }
}
