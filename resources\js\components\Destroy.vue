<script setup lang="ts">
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog';
import { LoaderCircle, Trash2, TriangleAlert } from 'lucide-vue-next';
import { useForm } from '@inertiajs/vue3';
import { toast } from 'vue-sonner';
import { ref } from 'vue';

// Runtime props definition with default
const props = defineProps({
    title: {
        type: String,
        default: 'Are you sure you want to delete this?'
    },
    description: {
        type: String,
        default: 'This action cannot be undone. Are you sure you want to permanently delete this file from servers?'
    },
    closeText: {
        type: String,
        default: 'No, Keep it'
    },
    submitText: {
        type: String,
        default: 'Yes, Delete it'
    },
    triggerTextShow: {
        type: Boolean,
        default: true
    },
    triggerText: {
        type: String,
        default: 'Delete'
    },
    url: {
        type: String,
        required: true
    },
    toastSuccess: {
        type: String,
        default: 'Deleted successfully'
    },
    toastError: {
        type: String,
        default: 'Failed to delete'
    }
});

const form = useForm({});
const isOpen = ref(false);

const url = ref(props.url);

const submit = () => {
    form.delete(url.value, {
        preserveScroll: true,
        onSuccess: (page: any) => {
            isOpen.value = false;
            const message = page?.props?.flash?.success || props.toastSuccess;
            toast.success(message);
        },
        onError: (page: any) => {
            const message = page?.props?.flash?.error || props.toastError;
            toast.error(message);
        }
    });
};
</script>

<template>
    <Dialog v-model:open="isOpen">
        <DialogTrigger as-child>
            <Button variant="outline" size="sm" class="hover:bg-red-50">
                <Trash2 class="h-4 w-4 text-red-600" />
                <span v-if="props.triggerTextShow" class="ml-1 hidden text-red-600 sm:inline"> {{ props.triggerText }} </span>
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-md">
            <div class="flex justify-center">
                <div class="flex items-center justify-center rounded-full bg-red-500/10 p-5">
                    <TriangleAlert class="h-8 w-8 text-red-600" />
                </div>
            </div>
            <DialogHeader>
                <DialogTitle class="text-center text-xl">{{ props.title }}</DialogTitle>
                <DialogDescription class="text-center">{{ props.description }}</DialogDescription>
            </DialogHeader>
            <div class="mt-4 flex items-center justify-center gap-3">
                <DialogClose as-child>
                    <Button type="button" variant="outline" size="lg"> {{ props.closeText }}</Button>
                </DialogClose>
                <Button
                    variant="destructive"
                    size="lg"
                    :disabled="form.processing"
                    @click="submit"
                >
                    <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin mr-2" />
                    {{ form.processing ? 'Deleting...' : props.submitText }}
                </Button>
            </div>
        </DialogContent>
    </Dialog>
</template>
