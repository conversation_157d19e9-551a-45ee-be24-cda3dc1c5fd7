<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IuranWarga extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'tanggal_jatuh_tempo' => 'date',
    ];

    // Relasi ke Jenis Iuran
    public function jenisIuran()
    {
        return $this->belongsTo(JenisIuran::class);
    }

    // Relasi ke Keluarga
    public function keluarga()
    {
        return $this->belongsTo(Keluarga::class);
    }

    // Relasi ke Pembayaran
    public function pembayaran()
    {
        return $this->hasMany(PembayaranIuran::class);
    }

    // Hitung total yang sudah dibayar
    public function totalDibayar()
    {
        return $this->pembayaran->sum('jumlah');
    }

    // Hitung sisa pembayaran
    public function sisaPembayaran()
    {
        return $this->jenisIuran->jumlah - $this->totalDibayar();
    }

    // Cek status lunas
    public function isLunas()
    {
        return $this->sisaPembayaran() <= 0;
    }
}
