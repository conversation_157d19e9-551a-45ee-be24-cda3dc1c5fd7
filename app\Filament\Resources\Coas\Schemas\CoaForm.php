<?php

namespace App\Filament\Resources\Coas\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class CoaForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('kode')
                    ->required(),
                TextInput::make('nama')
                    ->required(),
                Select::make('kategori')
                    ->options([
            'aset' => 'Aset',
            'kewajiban' => 'Kewajiban',
            'ekuitas' => 'Ekuitas',
            'pendapatan' => 'Pendapatan',
            'beban' => 'Beban',
        ])
                    ->required(),
                Select::make('tipe')
                    ->options(['header' => 'Header', 'detail' => 'Detail'])
                    ->required(),
                TextInput::make('parent_id')
                    ->numeric(),
                Textarea::make('deskripsi')
                    ->columnSpanFull(),
            ]);
    }
}
