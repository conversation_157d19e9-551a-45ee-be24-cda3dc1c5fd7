<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wargas', function (Blueprint $table) {
            $table->foreignId('keluarga_id')->nullable()->after('user_id')->constrained('keluargas')->onDelete('set null');
            $table->enum('status_hubungan', [
                'kepala_keluarga',
                'istri',
                'anak',
                'menantu',
                'cucu',
                'orang_tua',
                'mertua',
                'famili_lain',
                'pembantu',
                'lainnya'
            ])->default('kepala_keluarga')->after('keluarga_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wargas', function (Blueprint $table) {
            $table->dropIfExists('keluarga_id');
            $table->dropIfExists('status_hubungan');
        });
    }
};
