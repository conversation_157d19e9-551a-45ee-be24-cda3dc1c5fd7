(()=>{var n=({livewireId:i})=>({actionNestingIndex:null,init(){window.addEventListener("sync-action-modals",t=>{t.detail.id===i&&this.syncActionModals(t.detail.newActionNestingIndex)})},syncActionModals(t){if(this.actionNestingIndex!==t&&(this.actionNestingIndex!==null&&this.closeModal(),this.actionNestingIndex=t,this.actionNestingIndex!==null)){if(!this.$el.querySelector(`#${this.generateModalId(t)}`)){this.$nextTick(()=>this.openModal());return}this.openModal()}},generateModalId(t){return`fi-${i}-action-`+t},openModal(){let t=this.generateModalId(this.actionNestingIndex);this.$dispatch("open-modal",{id:t})},closeModal(){let t=this.generateModalId(this.actionNestingIndex);this.$dispatch("close-modal-quietly",{id:t})}});document.addEventListener("alpine:init",()=>{window.Alpine.data("filamentActionModals",n)});})();
