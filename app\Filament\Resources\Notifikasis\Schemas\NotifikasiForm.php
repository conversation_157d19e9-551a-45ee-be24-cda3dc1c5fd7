<?php

namespace App\Filament\Resources\Notifikasis\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class NotifikasiForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('user_id')
                    ->required()
                    ->numeric(),
                TextInput::make('judul')
                    ->required(),
                Textarea::make('pesan')
                    ->required()
                    ->columnSpanFull(),
                TextInput::make('tipe')
                    ->required(),
                Toggle::make('dibaca')
                    ->required(),
            ]);
    }
}
